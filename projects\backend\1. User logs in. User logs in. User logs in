1. User logs in
   ↓
2. Receives: Access Token (15 min) + Refresh Token (7 days)
   ↓
3. Uses Access Token for API calls
   ↓
4. After 15 minutes: Access Token expires
   ↓
5. Frontend automatically calls /api/auth/refresh-token/
   ↓
6. Receives: NEW Access Token (15 min) + NEW Refresh Token (7 days)
   ↓
7. Old Refresh Token is invalidated
   ↓
8. Repeat cycle

Monday 9:00 AM: Login
├── Access Token: expires Monday 9:15 AM
└── Refresh Token: expires Monday 9:00 AM + 7 days = Next Monday 9:00 AM

Monday 9:15 AM: Auto-refresh
├── NEW Access Token: expires Monday 9:30 AM  
└── NEW Refresh Token: expires Monday 9:15 AM + 7 days = Next Monday 9:15 AM

Monday 9:30 AM: Auto-refresh
├── NEW Access Token: expires Monday 9:45 AM
└── NEW Refresh Token: expires Monday 9:30 AM + 7 days = Next Monday 9:30 AM
