"""
Integration Tests for Registration Flow

This module contains integration tests for the complete user registration flow
to ensure all services work together correctly.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from user.models import User
from user.services.registration_orchestrator import RegistrationOrchestrator


class TestRegistrationIntegration(APITestCase):
    """Integration test cases for the complete registration flow"""

    def setUp(self):
        """Set up test fixtures"""
        self.factory = RequestFactory()
        self.orchestrator = RegistrationOrchestrator()

    @patch('user.services.registration_validation_service.rate_limiting_service')
    @patch('user.services.registration_validation_service.get_client_ip')
    @patch('user.services.registration_validation_service.get_dynamic_device_info')
    @patch('user.services.user_creation_service.detect_user_role_from_request')
    @patch('user.services.device_management_service.DeviceAuthenticationService')
    @patch('user.services.device_management_service.device_validation_service')
    @patch('user.services.activation_service.secure_token_service')
    @patch('user.services.activation_service.email_service')
    @patch('user.services.user_creation_service.Application.objects.create')
    @patch('user.services.user_creation_service.get_frontend_url_by_role')
    def test_complete_registration_flow_success(self, mock_frontend_url, mock_app_create,
                                              mock_email_service, mock_token_service,
                                              mock_device_validation, mock_device_auth,
                                              mock_detect_role, mock_device_info,
                                              mock_get_ip, mock_rate_service):
        """Test complete successful registration flow"""
        # Arrange
        mock_rate_service.check_rate_limit.return_value = (True, {})
        mock_get_ip.return_value = "***********"
        mock_device_info.return_value = {
            "device_name": "Test Device",
            "device_type": "web"
        }
        mock_detect_role.return_value = "farmer"
        mock_device_auth.register_device.return_value = None
        mock_device_validation.track_registration_device.return_value = {}
        mock_token_service.generate_token.return_value = ("test-token", Mock())
        mock_email_service.send_registration_activation.return_value = None
        mock_frontend_url.return_value = "https://farmer.example.com"
        mock_app_create.return_value = Mock(
            client_id="test-client-id",
            redirect_uris="https://farmer.example.com/auth/callback/"
        )

        request_data = {
            'email': '<EMAIL>',
            'name': 'Test User',
            'password': 'TestPassword123!',
            'device_id': '20240101120000_' + 'a' * 32,
            'device_type': 'web'
        }

        request = self.factory.post('/register/', request_data)
        request.data = request_data
        request.META['HTTP_USER_AGENT'] = 'Test User Agent'

        # Act
        response = self.orchestrator.register_user(request)

        # Assert
        self.assertEqual(response.status_code, 201)
        
        # Verify user was created
        user = User.objects.get(email='<EMAIL>')
        self.assertEqual(user.name, 'Test User')
        self.assertEqual(user.role, 'farmer')
        self.assertFalse(user.is_active)  # Should be inactive until activation

        # Verify response data
        response_data = response.data
        self.assertEqual(response_data['message'], 'Registration successful')
        self.assertTrue(response_data['data']['activation_required'])
        self.assertIn('user', response_data['data'])

    def test_registration_endpoint_success(self):
        """Test registration endpoint with real HTTP request"""
        # Arrange
        request_data = {
            'email': '<EMAIL>',
            'name': 'Integration Test User',
            'password': 'TestPassword123!',
            'opt_in': True
        }

        # Mock external dependencies
        with patch('user.services.registration_validation_service.rate_limiting_service') as mock_rate, \
             patch('user.services.registration_validation_service.get_client_ip') as mock_ip, \
             patch('user.services.registration_validation_service.get_dynamic_device_info') as mock_device_info, \
             patch('user.services.user_creation_service.detect_user_role_from_request') as mock_role, \
             patch('user.services.device_management_service.DeviceAuthenticationService') as mock_device_auth, \
             patch('user.services.device_management_service.device_validation_service') as mock_device_val, \
             patch('user.services.activation_service.secure_token_service') as mock_token, \
             patch('user.services.activation_service.email_service') as mock_email, \
             patch('user.services.user_creation_service.Application.objects.create') as mock_app, \
             patch('user.services.user_creation_service.get_frontend_url_by_role') as mock_frontend:

            # Setup mocks
            mock_rate.check_rate_limit.return_value = (True, {})
            mock_ip.return_value = "***********"
            mock_device_info.return_value = {
                "device_name": "Test Device",
                "device_type": "web"
            }
            mock_role.return_value = "farmer"
            mock_device_auth.register_device.return_value = None
            mock_device_val.track_registration_device.return_value = {}
            mock_token.generate_token.return_value = ("test-token", Mock())
            mock_email.send_registration_activation.return_value = None
            mock_frontend.return_value = "https://farmer.example.com"
            mock_app.return_value = Mock(
                client_id="test-client-id",
                redirect_uris="https://farmer.example.com/auth/callback/"
            )

            # Act
            response = self.client.post('/user/register/', request_data, format='json')

            # Assert
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            
            # Verify user was created in database
            user = User.objects.get(email='<EMAIL>')
            self.assertEqual(user.name, 'Integration Test User')
            self.assertEqual(user.role, 'farmer')
            self.assertTrue(user.opt_in)

    def test_registration_duplicate_email_error(self):
        """Test registration with duplicate email"""
        # Arrange - Create existing user
        User.objects.create_user(
            email='<EMAIL>',
            name='Existing User',
            password='TestPassword123!'
        )

        request_data = {
            'email': '<EMAIL>',
            'name': 'New User',
            'password': 'TestPassword123!',
        }

        # Mock external dependencies
        with patch('user.services.registration_validation_service.rate_limiting_service') as mock_rate, \
             patch('user.services.registration_validation_service.get_client_ip') as mock_ip, \
             patch('user.services.registration_validation_service.get_dynamic_device_info') as mock_device_info:

            mock_rate.check_rate_limit.return_value = (True, {})
            mock_ip.return_value = "***********"
            mock_device_info.return_value = {
                "device_name": "Test Device",
                "device_type": "web"
            }

            # Act
            response = self.client.post('/user/register/', request_data, format='json')

            # Assert
            self.assertEqual(response.status_code, status.HTTP_409_CONFLICT)
            self.assertIn('already exists', response.data['message'])

    def test_registration_validation_error(self):
        """Test registration with validation errors"""
        # Arrange - Invalid data (missing required fields)
        request_data = {
            'email': 'invalid-email',  # Invalid email format
            'password': '123',  # Too short password
        }

        # Mock external dependencies
        with patch('user.services.registration_validation_service.rate_limiting_service') as mock_rate, \
             patch('user.services.registration_validation_service.get_client_ip') as mock_ip, \
             patch('user.services.registration_validation_service.get_dynamic_device_info') as mock_device_info, \
             patch('user.services.user_creation_service.detect_user_role_from_request') as mock_role:

            mock_rate.check_rate_limit.return_value = (True, {})
            mock_ip.return_value = "***********"
            mock_device_info.return_value = {
                "device_name": "Test Device",
                "device_type": "web"
            }
            mock_role.return_value = "farmer"

            # Act
            response = self.client.post('/user/register/', request_data, format='json')

            # Assert
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            self.assertIn('errors', response.data)

    @patch('user.services.registration_validation_service.rate_limiting_service')
    def test_registration_rate_limit_error(self, mock_rate_service):
        """Test registration with rate limiting"""
        # Arrange
        mock_rate_service.check_rate_limit.return_value = (False, {"message": "Rate limited"})

        request_data = {
            'email': '<EMAIL>',
            'name': 'Test User',
            'password': 'TestPassword123!',
        }

        # Act
        response = self.client.post('/user/register/', request_data, format='json')

        # Assert
        self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)
        self.assertIn('Too many registration attempts', response.data['message'])


if __name__ == "__main__":
    pytest.main([__file__])
