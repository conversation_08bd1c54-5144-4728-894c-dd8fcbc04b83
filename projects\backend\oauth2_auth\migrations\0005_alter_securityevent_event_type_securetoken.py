# Generated by Django 5.2 on 2025-06-24 11:00

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("oauth2_auth", "0004_alter_securityevent_event_type"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name="securityevent",
            name="event_type",
            field=models.CharField(
                choices=[
                    ("login", "Login"),
                    ("logout", "Logout"),
                    ("failed_login", "Failed Login"),
                    ("account_locked", "Account Locked"),
                    ("account_unlocked", "Account Unlocked"),
                    ("account_permanently_locked", "Account Permanently Locked"),
                    ("locked_account_login_attempt", "Locked Account Login Attempt"),
                    (
                        "password_reset_attempt_locked_account",
                        "Password Reset Attempt on Locked Account",
                    ),
                    ("inactive_user_login_attempt", "Inactive User Login Attempt"),
                    ("token_issued", "Token Issued"),
                    ("token_refreshed", "Token Refreshed"),
                    ("token_revoked", "Token Revoked"),
                    ("token_expired", "Token Expired"),
                    ("invalid_token_usage", "Invalid Token Usage"),
                    ("token_ip_change", "Token IP Change"),
                    ("activation_token_generated", "Activation Token Generated"),
                    ("activation_token_used", "Activation Token Used"),
                    (
                        "password_reset_token_generated",
                        "Password Reset Token Generated",
                    ),
                    ("password_reset_token_used", "Password Reset Token Used"),
                    ("token_invalidated", "Token Invalidated"),
                    ("device_registered", "Device Registered"),
                    ("device_verified", "Device Verified"),
                    ("device_blocked", "Device Blocked"),
                    ("device_unblocked", "Device Unblocked"),
                    ("new_device_detected", "New Device Detected"),
                    ("device_fingerprint_changed", "Device Fingerprint Changed"),
                    ("suspicious_activity", "Suspicious Activity"),
                    ("brute_force_attempt", "Brute Force Attempt"),
                    ("rate_limit_exceeded", "Rate Limit Exceeded"),
                    ("ip_blocked", "IP Blocked"),
                    ("security_alert_sent", "Security Alert Sent"),
                    ("user_registered", "User Registered"),
                    ("account_activated", "Account Activated"),
                    ("password_changed", "Password Changed"),
                    ("password_reset_requested", "Password Reset Requested"),
                    ("password_reset_completed", "Password Reset Completed"),
                    ("email_changed", "Email Changed"),
                    ("profile_updated", "Profile Updated"),
                    ("oauth2_client_created", "OAuth2 Client Created"),
                    ("oauth2_client_revoked", "OAuth2 Client Revoked"),
                    ("client_secret_rotated", "Client Secret Rotated"),
                    ("authorization_granted", "Authorization Granted"),
                    ("authorization_denied", "Authorization Denied"),
                    ("admin_login", "Admin Login"),
                    ("admin_action", "Admin Action"),
                    ("user_impersonation", "User Impersonation"),
                    ("bulk_operation", "Bulk Operation"),
                    ("system_maintenance", "System Maintenance"),
                    ("api_access", "API Access"),
                    ("api_rate_limit", "API Rate Limit"),
                    ("api_error", "API Error"),
                    ("endpoint_access", "Endpoint Access"),
                    ("access_denied", "Access Denied"),
                    ("data_export", "Data Export"),
                    ("data_import", "Data Import"),
                    ("data_deletion", "Data Deletion"),
                    ("sensitive_data_access", "Sensitive Data Access"),
                ],
                max_length=50,
            ),
        ),
        migrations.CreateModel(
            name="SecureToken",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "token_type",
                    models.CharField(
                        choices=[
                            ("activation", "Account Activation"),
                            ("password_reset", "Password Reset"),
                            ("email_verification", "Email Verification"),
                            ("phone_verification", "Phone Verification"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "token_hash",
                    models.CharField(db_index=True, max_length=128, unique=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("used", "Used"),
                            ("expired", "Expired"),
                            ("invalidated", "Invalidated"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                ("ip_address", models.GenericIPAddressField()),
                ("user_agent", models.TextField()),
                (
                    "device_fingerprint",
                    models.CharField(blank=True, max_length=64, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("expires_at", models.DateTimeField()),
                ("used_at", models.DateTimeField(blank=True, null=True)),
                ("invalidated_at", models.DateTimeField(blank=True, null=True)),
                ("attempt_count", models.PositiveIntegerField(default=0)),
                ("max_attempts", models.PositiveIntegerField(default=3)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="secure_tokens",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Secure Token",
                "verbose_name_plural": "Secure Tokens",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "token_type", "status"],
                        name="oauth2_auth_user_id_89a49d_idx",
                    ),
                    models.Index(
                        fields=["expires_at"], name="oauth2_auth_expires_b08e72_idx"
                    ),
                    models.Index(
                        fields=["created_at"], name="oauth2_auth_created_c27e8a_idx"
                    ),
                    models.Index(
                        fields=["token_hash"], name="oauth2_auth_token_h_93420d_idx"
                    ),
                    models.Index(
                        fields=["status"], name="oauth2_auth_status_a39d11_idx"
                    ),
                ],
            },
        ),
    ]
