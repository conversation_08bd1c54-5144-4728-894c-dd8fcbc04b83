import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from crops.models import CropTransfer, Crops
from .models import InventoryQuantity, InventoryCropStatus
from user.models import RoleChoices
from rest_framework.decorators import (
    api_view,
    permission_classes,
    authentication_classes,
)

logger = logging.getLogger(__name__)

from rest_framework.authentication import SessionAuthentication, TokenAuthentication
from oauth2_provider.contrib.rest_framework import OAuth2Authentication
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from .serializers import (
    CropsWithStatusSerializer,
    InventoryQuantitySerializer,
    TraderInventoryQuantitySerializer,
)
from rest_framework import status
from django.db.models import F
from rest_framework import permissions
from oauth2_auth.permissions import AdminPermission
from user.permissions import UserStatusMixin
from agritram.message_utils import (
    handle_exception_with_logging,
    StandardSuccessResponse,
)
from agritram.exceptions import (
    raise_validation_error,
    raise_authorization_error,
    raise_not_found_error,
)

class InventoryViewPermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission for viewing inventory.
    Business Rule: Only traders and manufacturers can see trader inventory.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            # Generate unique ID for logging
            logger.warning(
                f"Inventory view permission denied for {request.user.email}",
                extra={
                    "operation": "INVENTORY_PERMISSION_DENIED",
                    "user_email": request.user.email,
                    "user_role": getattr(request.user, "role", "unknown"),
                    "denial_reason": reason,
                    "permission_type": "inventory_view",
                },
            )
            return False

        user_role = request.user.role

        # Only traders, manufacturers, and admins can view inventory
        return user_role in ["trader", "manufacturer", "admin"]


class InventoryOwnerPermission(permissions.BasePermission):
    """
    Permission for updating inventory.
    Business Rule: Only the trader who owns the inventory can update it.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        user_role = request.user.role

        # Only traders and admins can update inventory
        return user_role in ["trader", "admin"]


class TraderInventoryViewPermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission for viewing specific trader's inventory.
    Business Rule: Only that trader and manufacturers who want to buy can view.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            # Generate unique ID for logging
            logger.warning(
                f"Trader inventory view permission denied for {request.user.email}",
                extra={
                    "operation": "TRADER_INVENTORY_PERMISSION_DENIED",
                    "user_email": request.user.email,
                    "user_role": getattr(request.user, "role", "unknown"),
                    "denial_reason": reason,
                    "permission_type": "trader_inventory_view",
                },
            )
            return False

        user_role = request.user.role

        # Traders (owners), manufacturers (buyers), and admins can view
        return user_role in ["trader", "manufacturer", "admin"]

    def has_object_permission(self, request, view, obj):
        user_role = request.user.role

        # Admin can view everything
        if user_role == "admin":
            return True

        # Trader can view their own inventory
        if (
            user_role == "trader"
            and hasattr(obj, "trader")
            and obj.trader == request.user
        ):
            return True

        # Manufacturers can view any trader's inventory (for buying purposes)
        if user_role == "manufacturer":
            return True

        return False


@receiver(post_save, sender=CropTransfer)
def update_inventory(sender, instance, **kwargs):
    trader = instance.to_user
    if trader.role != RoleChoices.TRADER:
        return
    crop = instance.crop
    quantity = crop.quantity
    unit = crop.unit

    if unit != "tons":
        quantity = quantity / 1000

    inventory, create = InventoryQuantity.objects.get_or_create(
        trader=trader,
        defaults={
            "total_quantity_to_date": quantity,
            "total_quantity_in_storage": quantity,
            "total_quantity_batches": 1,
            "storage_batches": 1,
            "ready_to_sell_quantity": 0,
            "sold_quantity": 0,
            "ready_to_sell_batches": 0,
            "sold_batches": 0,
        },
    )
    inventorycropstatus, created = InventoryCropStatus.objects.get_or_create(
        trader=trader,
        crop=crop,
        defaults={
            "status": "storage",
        },
    )
    if create:
        inventory.save()
    else:
        inventory.total_quantity_to_date += quantity
        inventory.total_quantity_in_storage += quantity
        inventory.total_quantity_batches += 1
        inventory.storage_batches += 1
        inventory.save()
    if created:
        inventorycropstatus.save()
    else:
        pass


@api_view(["GET"])
@permission_classes(
    [InventoryViewPermission]
)  # Only traders and manufacturers can view inventory
@authentication_classes(
    [OAuth2Authentication, SessionAuthentication, TokenAuthentication]
)
def get_inventory(request):
    """
    Retrieve inventory data for the authenticated trader.
    Business Rule: Only traders and manufacturers can see trader inventory.
    """

    try:
        # Log incoming request
        logger.info(
            f"Get inventory request: {request.method} {request.path}",
            extra={"operation": "GET_INVENTORY_REQUEST"},
        )

        if request.user.role == "trader":
            # Traders can only see their own inventory
            logger.info(
                "Trader accessing own inventory",
                extra={
                    "operation": "INVENTORY_ACCESS",
                    "access_type": "own_inventory",
                },
            )
            inventory = get_object_or_404(InventoryQuantity, trader=request.user)
        elif request.user.role == "manufacturer":
            # Manufacturers should specify which trader's inventory they want to see
            logger.warning(
                "Manufacturer attempted to access general inventory endpoint",
                extra={
                    "operation": "INVENTORY_ACCESS_ERROR",
                    "error_type": "wrong_endpoint",
                },
            )
            raise_validation_error(
                message="Manufacturers should use /trader/{trader_id}/inventory/ endpoint",
                details="This endpoint is for individual trader inventory access",
            )
        elif request.user.role == "admin":
            # Admins can see all inventories, but this endpoint is for single trader
            logger.warning(
                "Admin attempted to access single trader inventory endpoint",
                extra={
                    "operation": "INVENTORY_ACCESS_ERROR",
                    "error_type": "wrong_endpoint",
                },
            )
            raise_validation_error(
                message="Admins should use /entire-inventory/ endpoint",
                details="This endpoint is for individual trader inventory access",
            )
        else:
            logger.warning(
                "Unauthorized role attempted to access inventory",
                extra={
                    "operation": "INVENTORY_ACCESS_DENIED",
                    "error_type": "unauthorized_role",
                },
            )
            raise_authorization_error(
                message="Access denied",
                details="Only traders, manufacturers, and admins can access inventory",
            )

        serializer = InventoryQuantitySerializer(inventory)

        # Log successful response
        logger.info(
            "Inventory retrieval response",
            extra={
                "operation": "GET_INVENTORY_SUCCESS",
                "status_code": 200,
                "message": "Inventory retrieved successfully",
            },
        )

        return StandardSuccessResponse.data_retrieved(
            message="Inventory retrieved successfully",
            details="Current inventory data for authenticated trader",
            data=serializer.data,
        )

    except Exception as e:
        return handle_exception_with_logging(e, "get_inventory", request, request.user)


@api_view(["GET"])
@permission_classes([AdminPermission])  # Only admins can view entire inventory
@authentication_classes(
    [OAuth2Authentication, SessionAuthentication, TokenAuthentication]
)
def get_entire_inventory(request):
    """
    Retrieve all inventory data.
    Business Rule: Only admins can view entire inventory.
    """

    try:
        # Log incoming request
        logger.info(
            f"Get entire inventory request: {request.method} {request.path}",
            extra={"operation": "GET_ENTIRE_INVENTORY_REQUEST"},
        )

        # Log admin access
        logger.info(
            "Admin accessing entire inventory",
            extra={
                "operation": "ENTIRE_INVENTORY_ACCESS",
                "access_type": "entire_inventory",
            },
        )

        inventory = InventoryQuantity.objects.all()
        serializer = TraderInventoryQuantitySerializer(inventory, many=True)

        # Log successful response
        logger.info(
            "Entire inventory retrieval response",
            extra={
                "operation": "GET_ENTIRE_INVENTORY_SUCCESS",
                "status_code": 200,
                "message": "Entire inventory retrieved successfully",
                "traders_count": len(serializer.data),
            },
        )

        return StandardSuccessResponse.data_retrieved(
            message="Entire inventory retrieved successfully",
            details=f"Retrieved inventory data for {len(serializer.data)} traders",
            data=serializer.data,
        )

    except Exception as e:
        return handle_exception_with_logging(
            e, "get_entire_inventory", request, request.user
        )


@api_view(["GET"])
@permission_classes(
    [InventoryViewPermission]
)  # Only traders and manufacturers can view crop status
@authentication_classes(
    [OAuth2Authentication, SessionAuthentication, TokenAuthentication]
)
def get_inventory_crop_status(request):
    """
    Get inventory crop status for the authenticated trader.
    Business Rule: Only traders can see their own crop status.
    """

    try:
        # Log incoming request
        logger.info(
            f"Get inventory crop status request: {request.method} {request.path}",
            extra={"operation": "GET_INVENTORY_CROP_STATUS_REQUEST"},
        )

        if request.user.role != "trader":
            logger.warning(
                "Non-trader attempted to access crop status",
                extra={
                    "operation": "CROP_STATUS_ACCESS_DENIED",
                    "error_type": "unauthorized_role",
                },
            )
            raise_authorization_error(
                message="Only traders can access their crop status",
                details="This endpoint is restricted to trader role users",
            )

        logger.info(
            "Trader accessing own crop status",
            extra={
                "operation": "CROP_STATUS_ACCESS",
                "access_type": "own_crop_status",
            },
        )

        inventory_status_qs = InventoryCropStatus.objects.filter(
            trader=request.user
        ).select_related("crop")
        serializer = CropsWithStatusSerializer(inventory_status_qs, many=True)

        # Log successful response
        logger.info(
            "Inventory crop status retrieval response",
            extra={
                "operation": "GET_INVENTORY_CROP_STATUS_SUCCESS",
                "status_code": 200,
                "message": "Inventory crop status retrieved successfully",
                "crops_count": len(serializer.data),
            },
        )

        return StandardSuccessResponse.data_retrieved(
            message="Inventory crop status retrieved successfully",
            details=f"Retrieved status for {len(serializer.data)} crops in your inventory",
            data=serializer.data,
        )

    except Exception as e:
        return handle_exception_with_logging(
            e, "get_inventory_crop_status", request, request.user
        )


@api_view(["GET"])
@permission_classes(
    [TraderInventoryViewPermission]
)  # Trader owners and manufacturers can view
@authentication_classes(
    [OAuth2Authentication, SessionAuthentication, TokenAuthentication]
)
def get_trader_inventory_crop_status(request, trader_id):
    """
    Get crop status for a specific trader's inventory.
    Business Rule: Only that trader and manufacturers who want to buy can view.
    """

    try:
        # Log incoming request
        logger.info(
            f"Get trader inventory crop status request: {request.method} {request.path}",
            extra={
                "operation": "GET_TRADER_INVENTORY_CROP_STATUS_REQUEST",
                "trader_id": trader_id,
            },
        )

        from user.models import User

        try:
            trader = User.objects.get(id=trader_id, role="trader")
        except User.DoesNotExist:
            logger.warning(
                f"Trader not found with ID {trader_id}",
                extra={
                    "operation": "TRADER_NOT_FOUND",    
                    "trader_id": trader_id,
                    "error_type": "trader_not_found",
                },
            )
            raise_not_found_error(
                message="Trader not found",
                details=f"No trader found with ID {trader_id}",
                resource_type="trader",
                resource_id=str(trader_id),
            )

        # Check permissions
        if request.user.role == "trader" and request.user != trader:
            logger.warning(
                "Trader attempted to access another trader's inventory",
                extra={
                    "operation": "TRADER_INVENTORY_ACCESS_DENIED",
                    "target_trader_id": trader_id,
                    "error_type": "cross_trader_access",
                },
            )
            raise_authorization_error(
                message="Traders can only view their own inventory",
                details="You can only access your own inventory data",
            )
        elif request.user.role not in ["trader", "manufacturer", "admin"]:
            logger.warning(
                "Unauthorized role attempted to access trader inventory",
                extra={
                    "operation": "TRADER_INVENTORY_ACCESS_DENIED",
                    "target_trader_id": trader_id,
                    "error_type": "unauthorized_role",
                },
            )
            raise_authorization_error(
                message="Access denied",
                details="Only traders, manufacturers, and admins can access inventory data",
            )

        logger.info(
            f"Accessing trader {trader_id} inventory crop status",
            extra={
                "operation": "TRADER_INVENTORY_ACCESS",
                "target_trader_id": trader_id,
                "access_type": "trader_inventory_crop_status",
            },
        )

        inventory_status_qs = InventoryCropStatus.objects.filter(
            trader=trader_id, status="ready"
        ).select_related("crop")
        serializer = CropsWithStatusSerializer(inventory_status_qs, many=True)

        # Log successful response
        logger.info(
            "Trader inventory crop status retrieval response",
            extra={
                "operation": "GET_TRADER_INVENTORY_CROP_STATUS_SUCCESS",
                "status_code": 200,
                "message": "Trader inventory crop status retrieved successfully",
                "trader_id": trader_id,
                "ready_crops_count": len(serializer.data),
            },
        )

        return StandardSuccessResponse.data_retrieved(
            message="Trader inventory crop status retrieved successfully",
            details=f"Retrieved {len(serializer.data)} ready crops from trader {trader_id}",
            data=serializer.data,
        )

    except Exception as e:
        return handle_exception_with_logging(
            e, "get_trader_inventory_crop_status", request, request.user
        )


@api_view(["PUT"])
@permission_classes([InventoryOwnerPermission])  # Only trader owners can update
@authentication_classes(
    [OAuth2Authentication, SessionAuthentication, TokenAuthentication]
)
def update_crop_status(request, crop_id):
    """
    Update crop status in inventory.
    Business Rule: Only the trader who owns the inventory can update it.
    """
    try:
        # Log incoming request
        logger.info(
            f"Update crop status request: {request.method} {request.path}",
            extra={"operation": "UPDATE_CROP_STATUS_REQUEST"},
        )

        # Ensure only traders can update their own inventory
        if request.user.role != "trader":
            logger.warning(
                "Non-trader attempted to update crop status",
                extra={
                    "operation": "CROP_STATUS_UPDATE_DENIED",
                    "crop_id": crop_id,
                    "error_type": "unauthorized_role",
                },
            )
            raise_authorization_error(
                message="Only traders can update crop status",
                details="This operation is restricted to trader role users",
            )

        crop = get_object_or_404(Crops, crop_id=crop_id)
        crop_status = request.data.get("status")

        logger.info(
            f"Starting crop status update for crop {crop_id}",
            extra={
                "operation": "CROP_STATUS_UPDATE_START",
                "crop_id": crop_id,
                "new_status": crop_status,
                "operation_type": "crop_status_update",
            },
        )

        # Verify the trader owns this crop in their inventory
        try:
            InventoryCropStatus.objects.get(crop=crop, trader=request.user)
        except InventoryCropStatus.DoesNotExist:
            logger.warning(
                f"Crop {crop_id} not found in trader's inventory",
                extra={
                    "operation": "CROP_NOT_IN_INVENTORY",
                    "crop_id": crop_id,
                    "error_type": "crop_not_owned",
                },
            )
            raise_not_found_error(
                message="Crop not found in your inventory",
                details=f"Crop with ID {crop_id} is not in your inventory",
                resource_type="inventory_crop",
                resource_id=crop_id,
            )

        # Update crop status
        InventoryCropStatus.objects.filter(crop=crop, trader=request.user).update(
            status=crop_status
        )

        logger.info(
            f"Crop status updated to {crop_status}",
            extra={
                "operation": "CROP_STATUS_UPDATED",
                "crop_id": crop_id,
                "new_status": crop_status,
                "operation_type": "status_update",
            },
        )

        # Update inventory quantities based on status change
        if crop_status == "storage":
            InventoryQuantity.objects.filter(trader=request.user).update(
                total_quantity_in_storage=F("total_quantity_in_storage")
                + crop.quantity,
                ready_to_sell_quantity=F("ready_to_sell_quantity") - crop.quantity,
                storage_batches=F("storage_batches") + 1,
                ready_to_sell_batches=F("ready_to_sell_batches") - 1,
            )
            logger.info(
                f"Inventory quantities updated for storage status",
                extra={
                    "operation": "INVENTORY_QUANTITIES_UPDATED",
                    "crop_id": crop_id,
                    "status_change": "to_storage",
                    "crop_quantity": crop.quantity,
                },
            )
        elif crop_status == "ready":
            InventoryQuantity.objects.filter(trader=request.user).update(
                total_quantity_in_storage=F("total_quantity_in_storage")
                - crop.quantity,
                ready_to_sell_quantity=F("ready_to_sell_quantity") + crop.quantity,
                storage_batches=F("storage_batches") - 1,
                ready_to_sell_batches=F("ready_to_sell_batches") + 1,
            )
            logger.info(
                f"Inventory quantities updated for ready status",
                extra={
                    "operation": "INVENTORY_QUANTITIES_UPDATED",    
                    "crop_id": crop_id,
                    "status_change": "to_ready",
                    "crop_quantity": crop.quantity,
                },
            )

        # Log successful response
        logger.info(
            "Crop status update response",
            extra={
                "operation": "UPDATE_CROP_STATUS_SUCCESS",
                "status_code": 200,
                "message": "Crop status updated successfully",
                "crop_id": crop_id,
                "new_status": crop_status,
            },
        )

        return StandardSuccessResponse.record_updated(
            message="Crop status updated successfully",
            details=f"Crop {crop_id} status changed to '{crop_status}' and inventory quantities updated",
            record_data={"crop_id": crop_id, "new_status": crop_status},
        )

    except Exception as e:
        return handle_exception_with_logging(
            e, "update_crop_status", request, request.user
        )
