#!/usr/bin/env python
"""
Test script to verify that generate_device_fingerprint is called with unique_id
"""

import os
import sys
import django
from unittest.mock import Mock, patch

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agritram.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    django.setup()
    print("✅ Django setup successful")
except Exception as e:
    print(f"❌ Django setup failed: {e}")
    sys.exit(1)

def test_device_fingerprint_with_unique_id():
    """Test that generate_device_fingerprint is called with unique_id"""
    try:
        from user.services.device_management_service import DeviceManagementService
        from django.test import RequestFactory
        
        factory = RequestFactory()
        service = DeviceManagementService()
        
        request = factory.post('/register/', {})
        request.META['HTTP_USER_AGENT'] = 'Test User Agent'
        
        with patch('user.services.device_management_service.get_dynamic_device_info') as mock_device_info, \
             patch('user.services.device_management_service.get_client_ip') as mock_client_ip, \
             patch('user.services.device_management_service.generate_device_fingerprint') as mock_fingerprint:
            
            mock_device_info.return_value = {
                "device_name": "Test Device",
                "device_type": "web"
            }
            mock_client_ip.return_value = "***********"
            mock_fingerprint.return_value = "test-fingerprint"
            
            # Call extract_device_info with unique_id
            result = service.extract_device_info(request, "registration", "test-unique-id")
            
            # Verify generate_device_fingerprint was called with unique_id
            mock_fingerprint.assert_called_once_with(request, "test-unique-id")
            
            print("✅ generate_device_fingerprint called correctly with unique_id")
            return True
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_device_fingerprint_auto_generate_unique_id():
    """Test that unique_id is auto-generated when not provided"""
    try:
        from user.services.device_management_service import DeviceManagementService
        from django.test import RequestFactory
        
        factory = RequestFactory()
        service = DeviceManagementService()
        
        request = factory.post('/register/', {})
        request.META['HTTP_USER_AGENT'] = 'Test User Agent'
        
        with patch('user.services.device_management_service.get_dynamic_device_info') as mock_device_info, \
             patch('user.services.device_management_service.get_client_ip') as mock_client_ip, \
             patch('user.services.device_management_service.generate_device_fingerprint') as mock_fingerprint, \
             patch('user.services.device_management_service.generate_unique_request_id') as mock_unique_id:
            
            mock_device_info.return_value = {
                "device_name": "Test Device",
                "device_type": "web"
            }
            mock_client_ip.return_value = "***********"
            mock_fingerprint.return_value = "test-fingerprint"
            mock_unique_id.return_value = "auto-generated-id"
            
            # Call extract_device_info without unique_id
            result = service.extract_device_info(request, "registration")
            
            # Verify unique_id was auto-generated
            mock_unique_id.assert_called_once()
            
            # Verify generate_device_fingerprint was called with auto-generated unique_id
            mock_fingerprint.assert_called_once_with(request, "auto-generated-id")
            
            print("✅ unique_id auto-generated and passed to generate_device_fingerprint")
            return True
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_registration_orchestrator_fingerprint():
    """Test that registration orchestrator calls generate_device_fingerprint correctly"""
    try:
        from user.services.registration_orchestrator import RegistrationOrchestrator
        from django.test import RequestFactory
        
        factory = RequestFactory()
        orchestrator = RegistrationOrchestrator()
        
        request = factory.post('/register/', {
            'email': '<EMAIL>',
            'name': 'Test User',
            'password': 'testpass123'
        })
        request.data = request.POST
        
        with patch('user.services.registration_orchestrator.get_client_ip') as mock_client_ip, \
             patch('user.services.registration_orchestrator.generate_device_fingerprint') as mock_fingerprint, \
             patch.object(orchestrator.validation_service, 'validate_registration_request') as mock_validate, \
             patch.object(orchestrator.user_service, 'handle_user_creation_flow') as mock_user_create, \
             patch.object(orchestrator.device_service, 'handle_device_registration_flow') as mock_device_flow, \
             patch.object(orchestrator.activation_service, 'handle_activation_flow') as mock_activation:
            
            mock_client_ip.return_value = "***********"
            mock_fingerprint.return_value = "test-fingerprint"
            
            # Mock successful responses
            mock_validate.return_value = {"email": "<EMAIL>", "device_id": "test-device"}
            mock_user_create.return_value = {
                "success": True,
                "user": Mock(id=1, email="<EMAIL>"),
                "serializer": Mock(data={"email": "<EMAIL>"}),
                "oauth2_application": Mock(client_id="test-client"),
                "oauth2_app_created": True
            }
            mock_device_flow.return_value = {"device_registered": True}
            mock_activation.return_value = {"email_sent": True}
            
            try:
                result = orchestrator.register_user(request)
                
                # Verify generate_device_fingerprint was called with a unique_id
                mock_fingerprint.assert_called_once()
                call_args = mock_fingerprint.call_args
                self.assertEqual(len(call_args[0]), 2)  # Should have request and unique_id
                
                print("✅ Registration orchestrator calls generate_device_fingerprint with unique_id")
                return True
                
            except Exception as e:
                print(f"❌ Registration orchestrator test failed: {e}")
                return False
            
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run fingerprint fix tests"""
    print("🧪 Testing generate_device_fingerprint Fix")
    print("=" * 50)
    
    tests = [
        test_device_fingerprint_with_unique_id,
        test_device_fingerprint_auto_generate_unique_id,
        test_registration_orchestrator_fingerprint,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\n🔍 Running {test.__name__}...")
        if test():
            passed += 1
        else:
            print(f"❌ {test.__name__} failed")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All fingerprint fix tests passed!")
        return True
    else:
        print("❌ Some tests failed. Please check the issues above.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
