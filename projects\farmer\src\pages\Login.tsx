import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import AuthLayout from '../components/AuthLayout'
import FormInput from '../components/FormInput'
import OTPVerification from '../components/OTPVerification'
import { useToast } from '../components/ui/use-toast'
import { login, loginWithOTP } from '../services/authService'
import { useAuthStore } from '../stores/authStore'

const Login = () => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [showOTPVerification, setShowOTPVerification] = useState(false)
  const [otpDeviceId, setOtpDeviceId] = useState('')
  const [otpDeviceName, setOtpDeviceName] = useState('')
  const { toast } = useToast()
  const navigate = useNavigate()

  // Use auth store
  const { login: loginUser, loading, setLoading, setError } = useAuthStore()

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {}

    if (!email) {
      newErrors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email is invalid'
    }

    if (!password) {
      newErrors.password = 'Password is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setLoading(true)

    try {
      console.log(email, password)
      const loginResponse = await login(email, password)

      // Use the auth store to handle login
      loginUser(loginResponse)

      toast({
        title: 'Success!',
        description: 'You have successfully logged in.',
      })

      // Check if user has account_address (wallet connected)
      if (!loginResponse.user.account_address) {
        navigate('/onboarding')
      } else {
        navigate('/dashboard')
      }
    } catch (error: any) {
      console.error('Login error:', error)

      // Check if OTP verification is required
      if (error.requiresOTP) {
        setOtpDeviceId(error.deviceId)
        setOtpDeviceName(error.deviceName)
        setShowOTPVerification(true)
        toast({
          title: 'Device Verification Required',
          description: 'Please check your email for a verification code.',
        })
      } else {
        setError('An error occurred during login. Please check your credentials.')
        toast({
          title: 'Error!',
          description: error.message || 'An error occurred during login. Please check your credentials.',
          variant: 'destructive'
        })
      }
    } finally {
      setLoading(false)
    }
  }

  const handleOTPSuccess = (loginResponse: any) => {
    // Use the auth store to handle login
    loginUser(loginResponse)

    toast({
      title: 'Success!',
      description: 'Device verified and logged in successfully.',
    })

    // Check if user has account_address (wallet connected)
    if (!loginResponse.user.account_address) {
      navigate('/onboarding')
    } else {
      navigate('/dashboard')
    }
  }

  const handleOTPCancel = () => {
    setShowOTPVerification(false)
    setOtpDeviceId('')
    setOtpDeviceName('')
  }

  return (
    <AuthLayout
      title={showOTPVerification ? "Device Verification" : "Welcome Back To Agritram"}
      subtitle={showOTPVerification ? "Verify your device to continue" : "Enter your credentials to access your account"}
    >
      {showOTPVerification ? (
        <OTPVerification
          email={email}
          password={password}
          deviceId={otpDeviceId}
          deviceName={otpDeviceName}
          onSuccess={handleOTPSuccess}
          onCancel={handleOTPCancel}
        />
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          <FormInput label="Email" type="email" value={email} onChange={setEmail} error={errors.email} placeholder="Enter your email" />
          <FormInput
            label="Password"
            type="password"
            value={password}
            onChange={setPassword}
            error={errors.password}
            placeholder="Enter your password"
          />

          <div className="flex items-center justify-between">
            <Link to="/forgot-password" className="text-sm text-link-text hover:text-link-text/80 font-bold">
              Forgot password?
            </Link>
          </div>

          <button
            type="submit"
            disabled={loading}
            className={`w-full py-3 px-4 bg-button-bg text-button-text rounded-lg font-medium border border-border-dotted border-dotted ${
              loading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-button-bg-hover'
            }`}
          >
            {loading ? 'Signing in...' : 'Sign In'}
          </button>

          <p className="text-center text-primary-text">
            <span className="opacity-60">Don't have an account? </span>
            <Link to="/register" className="text-link-text  hover:text-link-text/80 font-bold">
              Register now
            </Link>
          </p>
        </form>
      )}
    </AuthLayout>
  )
}

export default Login
