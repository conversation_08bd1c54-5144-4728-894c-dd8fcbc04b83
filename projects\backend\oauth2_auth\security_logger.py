"""
Comprehensive Security Logging Service
Handles all security event logging with proper audit trails
"""

import hashlib
import json
import time
from typing import Dict, Any, Optional, List, TYPE_CHECKING
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.conf import settings
from django.core.cache import cache
from django.db import transaction

from agritram.logger_utils import get_client_ip
from .models import SecurityEvent
import logging

logger = logging.getLogger(__name__)

if TYPE_CHECKING:
    from django.contrib.auth.models import AbstractUser


class SecurityLogger:
    """
    Centralized security logging service with advanced features
    """

    def __init__(self):
        self.high_risk_events = [
            "failed_login",
            "brute_force_attempt",
            "suspicious_activity",
            "account_locked",
            "device_blocked",
            "invalid_token_usage",
            "admin_login",
            "user_impersonation",
            "sensitive_data_access",
        ]

        self.critical_events = [
            "security_alert_sent",
            "ip_blocked",
            "oauth2_client_revoked",
            "mfa_disabled",
            "password_changed",
            "admin_action",
        ]

        # Compliance and audit settings
        self.compliance_events = [
            "data_access",
            "data_modification",
            "admin_action",
            "user_created",
            "user_deleted",
            "permission_changed",
            "sensitive_operation",
        ]

        # Immutable audit trail settings
        self.audit_chain_cache_key = "security_audit_chain"
        self.last_audit_hash_key = "last_audit_hash"

    def log_event(
        self,
        event_type: str,
        description: str,
        user: Optional["AbstractUser"] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        device_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        request=None,
    ) -> SecurityEvent:
        """
        Log a security event with comprehensive context
        """
        start_time = time.time()

        logger.info(f"SECURITY_EVENT_LOG_START | Starting security event logging: {event_type} | event_type={event_type} | description={description} | user_id={user.id if user else None} | user_email={user.email if user else None} | ip_address={ip_address} | device_id={device_id} | operation_type=security_event_logging")

        try:
            # Extract request information if provided
            if request:
                ip_address = ip_address or get_client_ip(request)
                user_agent = user_agent or request.META.get("HTTP_USER_AGENT", "")
                user = user or (
                    request.user
                    if hasattr(request, "user") and request.user.is_authenticated
                    else None
                )

            # Prepare metadata
            event_metadata = metadata or {}
            event_metadata.update(
                {
                    "timestamp": timezone.now().isoformat(),
                    "server_time": timezone.now().strftime("%Y-%m-%d %H:%M:%S UTC"),
                    "event_severity": self._get_event_severity(event_type),
                    "session_id": (
                        getattr(request, "session", {}).get("session_key")
                        if request
                        else None
                    ),
                }
            )

            # Log database operation start
            logger.info(f"DB_OPERATION | CREATE | oauth2_auth_securityevent | event_type={event_type} | user_id={user.id if user else None} | ip_address={ip_address} | event_severity={self._get_event_severity(event_type)}")

            # Create security event with immutable audit trail
            with transaction.atomic():
                security_event = SecurityEvent.objects.create(
                    user=user,
                    event_type=event_type,
                    description=description,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    device_id=device_id,
                    metadata=event_metadata,
                )

                # Log successful database operation
                logger.info(f"DB_OPERATION | CREATE SUCCESS | oauth2_auth_securityevent | event_type={event_type} | security_event_id={security_event.id} | user_id={user.id if user else None} | ip_address={ip_address} | event_severity={self._get_event_severity(event_type)}")

                # Add to immutable audit chain for compliance
                if event_type in self.compliance_events:
                    self._add_to_audit_chain(security_event)

            event_creation_duration = (time.time() - start_time) * 1000

            # Log performance metric
            logger.info(f"PERFORMANCE_METRIC | security_event_creation_time={event_creation_duration}ms | operation_type=security_event_creation | event_type={event_type} | security_event_id={security_event.id} | event_severity={self._get_event_severity(event_type)}")

            # Log security event with standardized logging
            logger.warning(f"SECURITY_EVENT | {event_type.upper()} | {description} | user_id={user.id if user else None} | security_event_id={security_event.id} | event_severity={self._get_event_severity(event_type)} | device_id={device_id} | event_creation_time_ms={event_creation_duration}")
    
            # Log business event
            logger.warning(f"BUSINESS_EVENT | SECURITY_EVENT_LOGGED | Security event logged: {event_type} | entity_type=SECURITY_EVENT | entity_id={security_event.id} | event_type={event_type} | user_id={user.id if user else None} | user_email={user.email if user else None} | ip_address={ip_address} | event_severity={self._get_event_severity(event_type)} | device_id={device_id} | event_creation_time_ms={event_creation_duration}")
            

            # Handle high-risk events
            if event_type in self.high_risk_events:
                self._handle_high_risk_event(security_event)

            # Handle critical events
            if event_type in self.critical_events:
                self._handle_critical_event(security_event)

            # Update security metrics
            self._update_security_metrics(event_type, user, ip_address)

            total_duration = (time.time() - start_time) * 1000

            logger.info(f"SECURITY_EVENT_LOG_SUCCESS | Security event logged successfully: {event_type} | event_type={event_type} | security_event_id={security_event.id} | user_id={user.id if user else None} | user_email={user.email if user else None} | ip_address={ip_address} | event_severity={self._get_event_severity(event_type)} | device_id={device_id} | event_creation_time_ms={event_creation_duration} | total_time_ms={total_duration}")

            return security_event

        except Exception as e:
            total_duration = (time.time() - start_time) * 1000

            # Log database operation failure
            logger.info(f"DB_OPERATION | CREATE FAILURE | oauth2_auth_securityevent | event_type={event_type} | user_id={user.id if user else None} | ip_address={ip_address} | error_type={e.__class__.__name__}")

            logger.error(f"SECURITY_EVENT_LOG_ERROR | Failed to log security event {event_type}: {str(e)} | event_type={event_type} | user_id={user.id if user else None} | user_email={user.email if user else None} | ip_address={ip_address} | error_type={e.__class__.__name__} | total_time_ms={total_duration}")

            return None

    def log_authentication_event(
        self,
        event_type: str,
        user: "AbstractUser",
        success: bool,
        request,
        additional_info: Optional[Dict] = None,
    ) -> SecurityEvent:
        """
        Log authentication-specific events
        """
        metadata = {
            "success": success,
            "authentication_method": "password",  # Default, can be overridden
            "user_id": user.id if user else None,
            "user_email": user.email if user else None,
            "user_role": user.role if user else None,
        }

        if additional_info:
            metadata.update(additional_info)

        description = f"Authentication {'successful' if success else 'failed'} for user {user.email if user else 'unknown'}"

        return self.log_event(
            event_type=event_type,
            description=description,
            user=user,
            request=request,
            metadata=metadata,
        )

    def log_token_event(
        self, event_type: str, user: "AbstractUser", token_info: Dict, request=None
    ) -> SecurityEvent:
        """
        Log token-related events
        """
        metadata = {
            "token_type": token_info.get("token_type", "access_token"),
            "client_id": token_info.get("client_id"),
            "scope": token_info.get("scope"),
            "expires_in": token_info.get("expires_in"),
            "token_id": token_info.get("token_id"),
        }

        description = f"Token {event_type.replace('token_', '')} for user {user.email}"

        return self.log_event(
            event_type=event_type,
            description=description,
            user=user,
            request=request,
            metadata=metadata,
        )

    def log_device_event(
        self, event_type: str, user: "AbstractUser", device_info: Dict, request=None
    ) -> SecurityEvent:
        """
        Log device-related events
        """
        metadata = {
            "device_id": device_info.get("device_id"),
            "device_name": device_info.get("device_name"),
            "device_type": device_info.get("device_type"),
            "fingerprint": device_info.get("fingerprint"),
            "is_trusted": device_info.get("is_trusted", False),
        }

        description = f"Device {event_type.replace('device_', '')} for user {user.email}: {device_info.get('device_name', 'Unknown')}"

        return self.log_event(
            event_type=event_type,
            description=description,
            user=user,
            request=request,
            device_id=device_info.get("device_id"),
            metadata=metadata,
        )

    def get_user_security_events(
        self,
        user: "AbstractUser",
        event_types: Optional[List[str]] = None,
        days: int = 30,
    ) -> List[SecurityEvent]:
        """
        Get security events for a specific user
        """
        try:
            cutoff_date = timezone.now() - timezone.timedelta(days=days)

            queryset = SecurityEvent.objects.filter(
                user=user, created_at__gte=cutoff_date
            )

            if event_types:
                queryset = queryset.filter(event_type__in=event_types)

            return list(queryset.order_by("-created_at"))

        except Exception as e:
            logger.error(f"GET_USER_SECURITY_EVENTS_ERROR | Failed to get security events for user {user.email}: {str(e)} | user_id={user.id} | user_email={user.email} | event_types={event_types} | days={days} | error_type={e.__class__.__name__}")
            return []

    def get_security_summary(self, days: int = 7) -> Dict:
        """
        Get security event summary for the specified period
        """
        try:
            cutoff_date = timezone.now() - timezone.timedelta(days=days)

            events = SecurityEvent.objects.filter(created_at__gte=cutoff_date)

            summary = {
                "total_events": events.count(),
                "unique_users": events.values("user").distinct().count(),
                "unique_ips": events.values("ip_address").distinct().count(),
                "event_types": {},
                "high_risk_events": 0,
                "critical_events": 0,
                "failed_logins": 0,
                "successful_logins": 0,
            }

            # Count events by type
            for event in events:
                event_type = event.event_type
                summary["event_types"][event_type] = (
                    summary["event_types"].get(event_type, 0) + 1
                )

                if event_type in self.high_risk_events:
                    summary["high_risk_events"] += 1

                if event_type in self.critical_events:
                    summary["critical_events"] += 1

                if event_type == "failed_login":
                    summary["failed_logins"] += 1
                elif event_type == "login":
                    summary["successful_logins"] += 1

            return summary

        except Exception as e:
            logger.error(f"GET_SECURITY_SUMMARY_ERROR | Failed to get security summary: {str(e)} | days={days} | error_type={e.__class__.__name__}")
            return {"error": str(e)}

    def _get_event_severity(self, event_type: str) -> str:
        """Get severity level for event type"""
        if event_type in self.critical_events:
            return "CRITICAL"
        elif event_type in self.high_risk_events:
            return "HIGH"
        elif event_type in ["login", "logout", "token_issued"]:
            return "LOW"
        else:
            return "MEDIUM"

    def _get_log_level_name(self, event_type: str) -> str:
        """Get log level name for event type"""
        if event_type in self.critical_events:
            return "CRITICAL"
        elif event_type in self.high_risk_events:
            return "WARNING"
        else:
            return "INFO"

    def _get_log_level(self, event_type: str) -> int:
        """Get Django log level integer for event type"""
        level_name = self._get_log_level_name(event_type)
        return {"CRITICAL": 50, "WARNING": 30, "INFO": 20}[level_name]

    def _format_log_message(self, event: SecurityEvent) -> str:
        """Format log message for Django logger"""
        user_info = f"User:{event.user.email}" if event.user else "User:Anonymous"
        ip_info = f"IP:{event.ip_address}" if event.ip_address else "IP:Unknown"
        device_info = f"Device:{event.device_id}" if event.device_id else ""

        return f"SECURITY[{event.event_type.upper()}] {event.description} | {user_info} | {ip_info} | {device_info}"

    def _handle_high_risk_event(
        self, event: SecurityEvent
    ):
        """Handle high-risk security events"""
        try:
            # Increment risk counter for user/IP
            if event.user:
                cache_key = f"security_risk_{event.user.id}"
                risk_count = cache.get(cache_key, 0) + 1
                cache.set(cache_key, risk_count, 3600)  # 1 hour

                # Alert if too many high-risk events
                if risk_count >= 5:
                    self._send_security_alert(
                        event.user, f"Multiple high-risk events detected: {risk_count}"
                    )

            if event.ip_address:
                # Use the enhanced rate limiting service for IP tracking and blocking
                try:
                    from .rate_limiting_service import rate_limiting_service

                    was_blocked = rate_limiting_service.track_suspicious_activity(
                        event.ip_address,
                        "suspicious_requests",  # Use the suspicious_requests category
                        None,  # No request object available in this context
                    )
                    if was_blocked:
                        logger.critical(f"SECURITY_EVENT | IP_BLOCKED_HIGH_RISK | IP {event.ip_address} blocked due to multiple high-risk security events | ip_address={event.ip_address} | security_event_id={event.id} | event_type={event.event_type} | risk_level=CRITICAL")
                except ImportError:
                    # Fallback to basic logging if rate limiting service is not available
                    logger.critical(f"SECURITY_EVENT | IP_HIGH_RISK_DETECTED | IP {event.ip_address} has multiple high-risk events - consider blocking | ip_address={event.ip_address} | security_event_id={event.id} | event_type={event.event_type} | risk_level=CRITICAL | action_needed=manual_review")

        except Exception as e:
            logger.error(f"HANDLE_HIGH_RISK_EVENT_ERROR | Failed to handle high-risk event: {str(e)} | security_event_id={event.id} | event_type={event.event_type} | user_id={event.user.id if event.user else None} | ip_address={event.ip_address} | error_type={e.__class__.__name__}")

    def _handle_critical_event(
        self, event: SecurityEvent
    ):
        """Handle critical security events"""
        try:
            # Always send immediate alert for critical events
            if event.user:
                self._send_security_alert(
                    event.user, f"Critical security event: {event.description}"
                )

            # Log to external security system if configured
            if hasattr(settings, "EXTERNAL_SECURITY_WEBHOOK"):
                self._send_to_external_system(event)

        except Exception as e:
            logger.error(f"HANDLE_CRITICAL_EVENT_ERROR | Failed to handle critical event: {str(e)} | security_event_id={event.id} | event_type={event.event_type} | user_id={event.user.id if event.user else None} | ip_address={event.ip_address} | error_type={e.__class__.__name__}")

    def _update_security_metrics(
        self,
        event_type: str,
        user: Optional["AbstractUser"],
        ip_address: Optional[str],          
    ):
        """Update security metrics in cache"""

        try:
            # Daily event counter
            today = timezone.now().date().isoformat()
            daily_key = f"security_events_{today}"
            cache.set(daily_key, cache.get(daily_key, 0) + 1, 86400)  # 24 hours

            # Event type counter
            type_key = f"security_event_type_{event_type}_{today}"
            cache.set(type_key, cache.get(type_key, 0) + 1, 86400)

        except Exception as e:
            logger.error(f"UPDATE_SECURITY_METRICS_ERROR | Failed to update security metrics: {str(e)} | event_type={event_type} | user_id={user.id if user else None} | ip_address={ip_address} | error_type={e.__class__.__name__}")

    def _send_security_alert(self, user: "AbstractUser", message: str):
        """Send security alert to user"""
        try:
            from .email_service import email_service

            email_service.send_security_alert(user, "Security Alert", message)
        except Exception as e:
            logger.error(f"SEND_SECURITY_ALERT_ERROR | Failed to send security alert: {str(e)} | user_id={user.id} | user_email={user.email} | alert_message={message} | error_type={e.__class__.__name__}")

    def _send_to_external_system(self, event: SecurityEvent):
        """Send event to external security monitoring system"""
        try:
            # Implementation for external security system integration
            # This could be SIEM, security monitoring service, etc.
            pass
        except Exception as e:
            logger.error(f"SEND_TO_EXTERNAL_SYSTEM_ERROR | Failed to send to external system: {str(e)} | security_event_id={event.id} | event_type={event.event_type} | user_id={event.user.id if event.user else None} | ip_address={event.ip_address} | error_type={e.__class__.__name__}")

    def _add_to_audit_chain(
        self, security_event: SecurityEvent 
    ):
        """
        Add security event to immutable audit chain for compliance
        Creates a blockchain-like audit trail
        """
        logger.info(f"AUDIT_CHAIN_ADD_START | Adding security event to audit chain: {security_event.id} | security_event_id={security_event.id} | event_type={security_event.event_type} | operation_type=audit_chain_addition")

        try:
            # Get the last audit hash
            last_hash = cache.get(self.last_audit_hash_key, "genesis")

            # Create audit record
            audit_record = {
                "event_id": str(security_event.id),
                "event_type": security_event.event_type,
                "user_id": str(security_event.user.id) if security_event.user else None,
                "timestamp": security_event.created_at.isoformat(),
                "ip_address": security_event.ip_address,
                "previous_hash": last_hash,
            }

            # Calculate hash of current record
            record_string = json.dumps(audit_record, sort_keys=True)
            current_hash = hashlib.sha256(record_string.encode()).hexdigest()
            audit_record["hash"] = current_hash

            # Store in audit chain (you might want to use a more permanent storage)
            chain_key = f"{self.audit_chain_cache_key}_{current_hash}"
            cache.set(chain_key, audit_record, 86400 * 365)  # Store for 1 year

            # Update last hash
            cache.set(self.last_audit_hash_key, current_hash, 86400 * 365)

            # Log business event for audit chain addition
            logger.warning(f"BUSINESS_EVENT | AUDIT_CHAIN_ENTRY_ADDED | Security event added to audit chain: {security_event.event_type} | entity_type=AUDIT_CHAIN | entity_id={current_hash} | security_event_id={security_event.id} | event_type={security_event.event_type} | audit_hash={current_hash} | previous_hash={last_hash}")

            logger.info(f"AUDIT_CHAIN_ADD_SUCCESS | Added event {security_event.id} to audit chain with hash {current_hash} | security_event_id={security_event.id} | event_type={security_event.event_type} | audit_hash={current_hash} | previous_hash={last_hash}")

        except Exception as e:
            logger.error(f"AUDIT_CHAIN_ADD_ERROR | Failed to add to audit chain: {str(e)} | security_event_id={security_event.id} | event_type={security_event.event_type} | error_type={e.__class__.__name__}")

    def verify_audit_chain_integrity(self) -> Dict[str, Any]:
        """
        Verify the integrity of the audit chain
        Returns verification results for compliance audits
        """
        try:
            # This is a simplified implementation
            # In production, you'd want to verify the entire chain
            last_hash = cache.get(self.last_audit_hash_key)

            if not last_hash:
                return {
                    "status": "empty",
                    "message": "No audit chain found",
                    "verified": True,
                }

            # Get the last audit record
            chain_key = f"{self.audit_chain_cache_key}_{last_hash}"
            last_record = cache.get(chain_key)

            if not last_record:
                return {
                    "status": "error",
                    "message": "Audit chain integrity compromised - missing record",
                    "verified": False,
                }

            # Verify hash
            record_copy = last_record.copy()
            stored_hash = record_copy.pop("hash")
            record_string = json.dumps(record_copy, sort_keys=True)
            calculated_hash = hashlib.sha256(record_string.encode()).hexdigest()

            if stored_hash != calculated_hash:
                return {
                    "status": "compromised",
                    "message": "Audit chain integrity compromised - hash mismatch",
                    "verified": False,
                    "expected_hash": calculated_hash,
                    "stored_hash": stored_hash,
                }

            return {
                "status": "verified",
                "message": "Audit chain integrity verified",
                "verified": True,
                "last_hash": last_hash,
                "last_record_timestamp": last_record.get("timestamp"),
            }

        except Exception as e:
            logger.error(f"VERIFY_AUDIT_CHAIN_ERROR | Error verifying audit chain: {str(e)} | error_type={e.__class__.__name__}")
            return {
                "status": "error",
                "message": f"Verification failed: {str(e)}",
                "verified": False,
            }

    def generate_compliance_report(
        self, start_date=None, end_date=None
    ) -> Dict[str, Any]:
        """
        Generate compliance report for audit purposes
        """
        try:
            from django.db.models import Count

            # Default to last 30 days if no dates provided
            if not start_date:
                start_date = timezone.now() - timezone.timedelta(days=30)
            if not end_date:
                end_date = timezone.now()

            # Get compliance events in date range
            compliance_events = SecurityEvent.objects.filter(
                event_type__in=self.compliance_events,
                created_at__range=[start_date, end_date],
            )

            # Generate statistics
            event_stats = (
                compliance_events.values("event_type")
                .annotate(count=Count("id"))
                .order_by("-count")
            )

            user_stats = (
                compliance_events.filter(user__isnull=False)
                .values("user__email")
                .annotate(count=Count("id"))
                .order_by("-count")[:10]
            )  # Top 10 users

            ip_stats = (
                compliance_events.filter(ip_address__isnull=False)
                .values("ip_address")
                .annotate(count=Count("id"))
                .order_by("-count")[:10]
            )  # Top 10 IPs

            # Verify audit chain integrity
            chain_integrity = self.verify_audit_chain_integrity()

            return {
                "report_generated_at": timezone.now().isoformat(),
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                },
                "total_compliance_events": compliance_events.count(),
                "event_type_breakdown": list(event_stats),
                "top_users": list(user_stats),
                "top_ip_addresses": list(ip_stats),
                "audit_chain_integrity": chain_integrity,
                "compliance_status": (
                    "VERIFIED" if chain_integrity["verified"] else "COMPROMISED"
                ),
            }

        except Exception as e:
            logger.error(f"GENERATE_COMPLIANCE_REPORT_ERROR | Error generating compliance report: {str(e)} | error_type={e.__class__.__name__}")
            return {"error": str(e), "report_generated_at": timezone.now().isoformat()}


# Global security logger instance
security_logger = SecurityLogger()
