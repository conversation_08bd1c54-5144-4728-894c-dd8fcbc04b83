"""
Custom CORS middleware to handle security headers that django-cors-headers doesn't support properly
"""
from django.http import HttpResponse
from django.conf import settings


class CustomCorsMiddleware:
    """
    Custom CORS middleware to handle additional security headers
    that django-cors-headers is not properly allowing.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        # Get allowed origins from settings
        self.allowed_origins = getattr(settings, 'CORS_ALLOWED_ORIGINS', [])
        
        # Security headers that need to be allowed
        self.security_headers = [
            'x-security-level',
            'x-request-timestamp',
            'x-request-nonce',
            'x-request-signature',
            'x-device-fingerprint',
            'x-client-version',
            'x-device-security-score',
            'x-security-threats',
            'x-risk-level',
            'x-device-id',
            'x-api-key',
        ]
        
        # Standard headers
        self.standard_headers = [
            'accept',
            'accept-encoding',
            'authorization',
            'content-type',
            'dnt',
            'origin',
            'user-agent',
            'x-csrftoken',
            'x-requested-with',
        ]
        
        # All allowed headers
        self.all_allowed_headers = self.standard_headers + self.security_headers

    def __call__(self, request):
        # Check if this is a CORS preflight request
        if request.method == 'OPTIONS' and 'HTTP_ORIGIN' in request.META:
            return self.handle_preflight(request)
        
        # Process the request normally
        response = self.get_response(request)
        
        # Add CORS headers to the response if origin is allowed
        origin = request.META.get('HTTP_ORIGIN')
        if origin and self.is_origin_allowed(origin):
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Expose-Headers'] = 'x-ratelimit-limit, x-ratelimit-remaining, x-ratelimit-reset'
        
        return response
    
    def handle_preflight(self, request):
        """Handle CORS preflight requests"""
        origin = request.META.get('HTTP_ORIGIN')
        
        if not origin or not self.is_origin_allowed(origin):
            return HttpResponse(status=403)
        
        # Get requested headers
        requested_headers = request.META.get('HTTP_ACCESS_CONTROL_REQUEST_HEADERS', '')
        requested_headers_list = [h.strip().lower() for h in requested_headers.split(',') if h.strip()]
        
        # Check if all requested headers are allowed
        allowed_headers = []
        for header in requested_headers_list:
            if header in [h.lower() for h in self.all_allowed_headers]:
                allowed_headers.append(header)
        
        # Create preflight response
        response = HttpResponse()
        response['Access-Control-Allow-Origin'] = origin
        response['Access-Control-Allow-Credentials'] = 'true'
        response['Access-Control-Allow-Methods'] = 'DELETE, GET, OPTIONS, PATCH, POST, PUT'
        response['Access-Control-Allow-Headers'] = ', '.join(self.all_allowed_headers)
        response['Access-Control-Max-Age'] = '86400'  # 24 hours
        response['Access-Control-Expose-Headers'] = 'x-ratelimit-limit, x-ratelimit-remaining, x-ratelimit-reset'
        
        return response
    
    def is_origin_allowed(self, origin):
        """Check if the origin is in the allowed list"""
        return origin in self.allowed_origins
