"""
Enhanced logging utilities that integrate with existing correlation ID system
while using standard Python logging practices.

This module provides a bridge between your custom logging functions and 
standard Python logging, maintaining correlation ID functionality.
"""

import logging
import json
import inspect
from typing import Dict, Any, Optional
from django.utils import timezone
from .correlation_id_middleware import get_correlation_id
from .logger_utils import get_client_ip, get_caller_info


class CorrelationLogger:
    """
    Enhanced logger that automatically includes correlation IDs and caller information.
    
    Usage:
        logger = CorrelationLogger(__name__)
        logger.info("User logged in", extra={"user_id": 123})
        logger.error("Database error", exc_info=True)
    """
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.name = name
    
    def _get_extra_context(self, extra: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Get enhanced context including correlation ID and caller info."""
        context = extra or {}
        
        # Add correlation ID
        correlation_id = get_correlation_id()
        if correlation_id:
            context['correlation_id'] = correlation_id
        
        # Add caller information for better debugging
        caller_info = get_caller_info()
        if caller_info and caller_info.get('filename') != 'unknown':
            context.update({
                'caller_file': caller_info['filename'],
                'caller_line': caller_info['line_number'],
                'caller_function': caller_info['function_name']
            })
        
        return context
    
    def info(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log info message with correlation ID."""
        enhanced_extra = self._get_extra_context(extra)
        self.logger.info(message, extra=enhanced_extra)
    
    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log warning message with correlation ID."""
        enhanced_extra = self._get_extra_context(extra)
        self.logger.warning(message, extra=enhanced_extra)
    
    def error(self, message: str, extra: Optional[Dict[str, Any]] = None, exc_info: bool = False):
        """Log error message with correlation ID."""
        enhanced_extra = self._get_extra_context(extra)
        self.logger.error(message, extra=enhanced_extra, exc_info=exc_info)
    
    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log debug message with correlation ID."""
        enhanced_extra = self._get_extra_context(extra)
        self.logger.debug(message, extra=enhanced_extra)
    
    def critical(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log critical message with correlation ID."""
        enhanced_extra = self._get_extra_context(extra)
        self.logger.critical(message, extra=enhanced_extra)


def get_enhanced_logger(name: str) -> CorrelationLogger:
    """
    Get an enhanced logger instance for the given name.
    
    Args:
        name: Logger name (typically __name__)
    
    Returns:
        CorrelationLogger instance
    """
    return CorrelationLogger(name)


def log_operation(
    operation_type: str,
    message: str,
    level: str = "INFO",
    metadata: Optional[Dict[str, Any]] = None,
    logger_name: Optional[str] = None
):
    """
    Enhanced replacement for log_operation_info that uses standard logging.
    
    Args:
        operation_type: Type of operation (USER_LOGIN, CROP_RETRIEVAL, etc.)
        message: Descriptive message
        level: Log level (INFO, WARNING, ERROR)
        metadata: Additional metadata
        logger_name: Logger name (defaults to caller's module)
    """
    # Get logger name from caller if not provided
    if not logger_name:
        frame = inspect.currentframe().f_back
        logger_name = frame.f_globals.get('__name__', 'unknown')
    
    logger = get_enhanced_logger(logger_name)
    
    # Prepare enhanced message with operation type
    enhanced_message = f"{operation_type}: {message}"
    
    # Prepare extra context
    extra = {
        'operation_type': operation_type,
        'timestamp': timezone.now().isoformat(),
    }
    
    if metadata:
        extra.update(metadata)
    
    # Log at appropriate level
    if level.upper() == "ERROR":
        logger.error(enhanced_message, extra=extra)
    elif level.upper() == "WARNING":
        logger.warning(enhanced_message, extra=extra)
    elif level.upper() == "DEBUG":
        logger.debug(enhanced_message, extra=extra)
    else:
        logger.info(enhanced_message, extra=extra)


def log_request_enhanced(request, operation_type: str = "REQUEST", logger_name: Optional[str] = None):
    """
    Enhanced request logging that maintains compatibility with existing log_request_info
    but uses standard Python logging.
    
    Args:
        request: Django request object
        operation_type: Type of operation
        logger_name: Logger name (defaults to caller's module)
    """
    if not logger_name:
        frame = inspect.currentframe().f_back
        logger_name = frame.f_globals.get('__name__', 'unknown')
    
    logger = get_enhanced_logger(logger_name)
    
    # Extract request information
    extra = {
        'operation_type': operation_type,
        'method': getattr(request, 'method', 'UNKNOWN'),
        'path': getattr(request, 'path', ''),
        'client_ip': get_client_ip(request),
        'user_agent': request.META.get('HTTP_USER_AGENT', '') if hasattr(request, 'META') else '',
        'timestamp': timezone.now().isoformat(),
    }
    
    # Add user information if available
    if hasattr(request, 'user') and request.user.is_authenticated:
        extra.update({
            'user_id': request.user.id,
            'user_email': getattr(request.user, 'email', ''),
        })
    
    # Extract headers safely (limited for security)
    if hasattr(request, 'headers'):
        safe_headers = {
            k: v for k, v in dict(request.headers).items() 
            if k.lower() not in ['authorization', 'cookie', 'x-api-key']
        }
        extra['headers_count'] = len(safe_headers)
    
    message = f"Request: {extra['method']} {extra['path']}"
    logger.info(message, extra=extra)


def log_response_enhanced(
    response_data: Any, 
    status_code: int = 200, 
    operation_type: str = "RESPONSE",
    logger_name: Optional[str] = None
):
    """
    Enhanced response logging using standard Python logging.
    
    Args:
        response_data: Response data to log
        status_code: HTTP status code
        operation_type: Type of operation
        logger_name: Logger name (defaults to caller's module)
    """
    if not logger_name:
        frame = inspect.currentframe().f_back
        logger_name = frame.f_globals.get('__name__', 'unknown')
    
    logger = get_enhanced_logger(logger_name)
    
    extra = {
        'operation_type': operation_type,
        'status_code': status_code,
        'timestamp': timezone.now().isoformat(),
    }
    
    # Add response data summary (avoid logging sensitive data)
    if isinstance(response_data, dict):
        extra['response_keys'] = list(response_data.keys())
        if 'message' in response_data:
            extra['response_message'] = response_data['message']
    
    message = f"Response: {status_code} - {operation_type}"
    
    # Log at appropriate level based on status code
    if status_code >= 500:
        logger.error(message, extra=extra)
    elif status_code >= 400:
        logger.warning(message, extra=extra)
    else:
        logger.info(message, extra=extra)


# Convenience function for backward compatibility
def create_logger(name: str) -> CorrelationLogger:
    """Create a correlation-aware logger. Alias for get_enhanced_logger."""
    return get_enhanced_logger(name)
