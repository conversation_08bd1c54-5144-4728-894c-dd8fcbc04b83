import logging
from django.contrib.auth.backends import ModelBackend
from django.utils import timezone
from .models import User
from .auth_utils import UserStatusValidator, log_authentication_attempt
from oauth2_auth.utils import get_client_ip

logger = logging.getLogger(__name__)


class EmailBackend(ModelBackend):
    def authenticate(self, request, email=None, password=None, **kwargs):
        import time

        start_time = time.time()
        # Log authentication attempt start
        logger.info(
            f"OPERATION_INFO: AUTHENTICATION_START | message=Starting email backend authentication for: {email} | metadata="
            f"{{'authentication_method': 'email_password', 'attempted_email': '{email}', 'client_ip': '{get_client_ip(request) if request else None}', 'user_agent': '{request.META.get('HTTP_USER_AGENT', '') if request else None}'}}"
        )

        try:
            # Log database operation for user lookup
            logger.info(
                f"DATABASE_OPERATION: SELECT on User | metadata={{'lookup_field': 'email', 'lookup_value': '{email}'}}"
            )

            user = User.objects.get(email=email)

            # Log successful user lookup
            logger.info(
                f"DATABASE_OPERATION: SELECT on User | result=SUCCESS | metadata={{'lookup_field': 'email', 'lookup_value': '{email}', 'user_id': {user.id}, 'user_found': True}}"
            )

            # Use centralized user status validation
            validation_result = UserStatusValidator.validate_user_status(
                user, log_violations=True
            )

            if not validation_result["is_valid"]:
                # Log failed authentication attempt with specific error code
                log_authentication_attempt(
                    user=user,
                    success=False,
                    method="password",
                    ip_address=get_client_ip(request) if request else None,
                    user_agent=(
                        request.META.get("HTTP_USER_AGENT", "") if request else None
                    ),
                    error_code=validation_result["error_code"],
                    request=request,
                )

                # Log operation failure with detailed information
                auth_time = (time.time() - start_time) * 1000
                logger.warning(
                    f"OPERATION_INFO: AUTHENTICATION_BLOCKED | message=Authentication blocked for user {email}: {validation_result['error_message']} | metadata="
                    f"{{'user_id': {user.id}, 'user_email': '{email}', 'authentication_method': 'email_password', 'authentication_result': 'blocked', 'block_reason': 'user_status_violation', 'error_code': '{validation_result['error_code']}', 'error_message': '{validation_result['error_message']}', 'authentication_time_ms': {auth_time}}}"
                )
                return None

            # Verify password
            if user.check_password(password):
                # Log successful authentication
                log_authentication_attempt(
                    user=user,
                    success=True,
                    method="password",
                    ip_address=get_client_ip(request) if request else None,
                    user_agent=(
                        request.META.get("HTTP_USER_AGENT", "") if request else None
                    ),
                    request=request,
                )

                # Log database operation for updating last login
                logger.info(
                    f"DATABASE_OPERATION: UPDATE on User | metadata={{'user_id': {user.id}, 'updated_fields': ['last_login'], 'operation_reason': 'successful_authentication'}}"
                )

                # Update last login and online status
                user.last_login = timezone.now()
                user.save(update_fields=["last_login"])

                # Log successful database update
                logger.info(
                    f"DATABASE_OPERATION: UPDATE on User | result=SUCCESS | metadata={{'user_id': {user.id}, 'updated_fields': ['last_login'], 'last_login_timestamp': '{user.last_login.isoformat()}', 'operation_reason': 'successful_authentication'}}"
                )

                # Calculate and log performance metrics
                auth_time = (time.time() - start_time) * 1000
                logger.info(
                    f"PERFORMANCE_METRIC: authentication_time | value={auth_time} ms | operation_type=email_backend_authentication | metadata="
                    f"{{'authentication_method': 'email_password', 'authentication_result': 'success', 'user_id': {user.id}, 'user_email': '{email}'}}"
                )

                # Log successful authentication operation
                logger.info(
                    f"OPERATION_INFO: AUTHENTICATION_SUCCESS | message=Successful email backend authentication for user: {email} | metadata="
                    f"{{'user_id': {user.id}, 'user_email': '{email}', 'authentication_method': 'email_password', 'authentication_result': 'success', 'authentication_time_ms': {auth_time}, 'last_login_updated': True}}"
                )

                return user
            else:
                # Log failed password authentication
                log_authentication_attempt(
                    user=user,
                    success=False,
                    method="password",
                    ip_address=get_client_ip(request) if request else None,
                    user_agent=(
                        request.META.get("HTTP_USER_AGENT", "") if request else None
                    ),
                    error_code="INVALID_PASSWORD",
                    request=request,
                )

                # Calculate and log performance metrics for failed authentication
                auth_time = (time.time() - start_time) * 1000
                logger.info(
                    f"PERFORMANCE_METRIC: authentication_time | value={auth_time} ms | operation_type=email_backend_authentication | metadata="
                    f"{{'authentication_method': 'email_password', 'authentication_result': 'failed', 'failure_reason': 'invalid_password', 'user_id': {user.id}, 'user_email': '{email}'}}"
                )

                # Log failed authentication operation
                logger.warning(
                    f"OPERATION_INFO: AUTHENTICATION_FAILED | message=Failed password authentication for user: {email} | metadata="
                    f"{{'user_id': {user.id}, 'user_email': '{email}', 'authentication_method': 'email_password', 'authentication_result': 'failed', 'failure_reason': 'invalid_password', 'error_code': 'INVALID_PASSWORD', 'authentication_time_ms': {auth_time}}}"
                )

                return None

        except User.DoesNotExist:
            # Log failed authentication attempt for non-existent user
            log_authentication_attempt(
                user=None,
                success=False,
                method="password",
                ip_address=get_client_ip(request) if request else None,
                user_agent=request.META.get("HTTP_USER_AGENT", "") if request else None,
                error_code="USER_NOT_FOUND",
                additional_info={"attempted_email": email},
            )

            return None

    def get_user(self, user_id):
        try:
            user = User.objects.get(pk=user_id)

            # Use centralized user status validation for session retrieval
            validation_result = UserStatusValidator.validate_user_status(
                user, log_violations=True
            )

            if not validation_result["is_valid"]:
                logger.warning(
                    f"OPERATION_INFO: SESSION_RETRIEVAL_BLOCKED | message=Session retrieval blocked for user {user.email}: {validation_result['error_message']} | metadata="
                    f"{{'user_id': {user.id}, 'user_email': '{user.email}'}}"
                )
                return None

            return user
        except User.DoesNotExist:
            return None
