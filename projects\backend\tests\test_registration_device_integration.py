"""
Integration test for device validation during user registration
"""

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.urls import reverse
from unittest.mock import patch
import json

from oauth2_auth.models import DeviceToken

User = get_user_model()


class RegistrationDeviceIntegrationTest(TestCase):
    """Integration test for device validation in registration"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()
        cache.clear()
        
        # Create a test user with existing device
        self.existing_user = User.objects.create_user(
            email="<EMAIL>",
            name="Existing User",
            password="testpass123"
        )
        
        # Create an existing device
        self.existing_device = DeviceToken.objects.create(
            user=self.existing_user,
            device_id="existing_device_12345678901234567890123456789012345678901234567890",
            device_name="Existing Device",
            device_type="web",
            fingerprint="existing_fingerprint",
            ip_address="*************",
            user_agent="Mozilla/5.0 Test Browser"
        )

    @patch('oauth2_auth.email_service.email_service.send_registration_activation')
    @patch('oauth2_auth.authentication.DeviceAuthenticationService.register_device')
    def test_registration_with_new_device(self, mock_register_device, mock_send_email):
        """Test registration with a new device"""
        mock_register_device.return_value = True
        mock_send_email.return_value = True
        
        registration_data = {
            'name': 'Test User',
            'email': '<EMAIL>',
            'password': 'testpass123',
            'device_id': 'new_device_12345678901234567890123456789012345678901234567890',
            'device_type': 'web'
        }
        
        response = self.client.post(
            '/user/register/',
            data=json.dumps(registration_data),
            content_type='application/json',
            HTTP_USER_AGENT='Mozilla/5.0 Test Browser',
            REMOTE_ADDR='192.168.1.101'
        )
        
        self.assertEqual(response.status_code, 201)
        response_data = response.json()
        self.assertIn('device_id', response_data)
        self.assertEqual(response_data['device_id'], registration_data['device_id'])

    @patch('oauth2_auth.email_service.email_service.send_registration_activation')
    def test_registration_with_invalid_device_type(self, mock_send_email):
        """Test registration with invalid device_type"""
        mock_send_email.return_value = True
        
        registration_data = {
            'name': 'Test User',
            'email': '<EMAIL>',
            'password': 'testpass123',
            'device_id': 'test_device_12345678901234567890123456789012345678901234567890',
            'device_type': 'invalid_type'  # Invalid device type
        }
        
        response = self.client.post(
            '/user/register/',
            data=json.dumps(registration_data),
            content_type='application/json',
            HTTP_USER_AGENT='Mozilla/5.0 Test Browser',
            REMOTE_ADDR='192.168.1.101'
        )
        
        self.assertEqual(response.status_code, 400)
        response_data = response.json()
        self.assertIn('error', response_data)
        self.assertIn('Invalid device type', response_data['error']['message'])

    @patch('oauth2_auth.email_service.email_service.send_registration_activation')
    def test_registration_with_existing_device_type_mismatch(self, mock_send_email):
        """Test registration with existing device but mismatched type"""
        mock_send_email.return_value = True
        
        registration_data = {
            'name': 'Test User',
            'email': '<EMAIL>',
            'password': 'testpass123',
            'device_id': self.existing_device.device_id,
            'device_type': 'mobile'  # Different from stored 'web'
        }
        
        response = self.client.post(
            '/user/register/',
            data=json.dumps(registration_data),
            content_type='application/json',
            HTTP_USER_AGENT='Mozilla/5.0 Test Browser',
            REMOTE_ADDR='*************'
        )
        
        self.assertEqual(response.status_code, 400)
        response_data = response.json()
        self.assertIn('error', response_data)
        self.assertIn('Device validation failed', response_data['error']['message'])

    @patch('oauth2_auth.email_service.email_service.send_registration_activation')
    @patch('oauth2_auth.authentication.DeviceAuthenticationService.register_device')
    def test_registration_with_existing_device_same_type(self, mock_register_device, mock_send_email):
        """Test registration with existing device and same type (should work)"""
        mock_register_device.return_value = True
        mock_send_email.return_value = True
        
        registration_data = {
            'name': 'Test User',
            'email': '<EMAIL>',
            'password': 'testpass123',
            'device_id': self.existing_device.device_id,
            'device_type': 'web'  # Same as stored type
        }
        
        response = self.client.post(
            '/user/register/',
            data=json.dumps(registration_data),
            content_type='application/json',
            HTTP_USER_AGENT='Mozilla/5.0 Test Browser',
            REMOTE_ADDR='*************'
        )
        
        self.assertEqual(response.status_code, 201)
        response_data = response.json()
        self.assertIn('device_id', response_data)
        self.assertEqual(response_data['device_id'], self.existing_device.device_id)

    @patch('oauth2_auth.email_service.email_service.send_registration_activation')
    @patch('oauth2_auth.authentication.DeviceAuthenticationService.register_device')
    def test_registration_without_device_type_uses_detection(self, mock_register_device, mock_send_email):
        """Test registration without device_type uses automatic detection"""
        mock_register_device.return_value = True
        mock_send_email.return_value = True
        
        registration_data = {
            'name': 'Test User',
            'email': '<EMAIL>',
            'password': 'testpass123',
            'device_id': 'test_device_12345678901234567890123456789012345678901234567890',
            # No device_type provided - should be auto-detected
        }
        
        response = self.client.post(
            '/user/register/',
            data=json.dumps(registration_data),
            content_type='application/json',
            HTTP_USER_AGENT='Mozilla/5.0 Test Browser',  # Should detect as 'web'
            REMOTE_ADDR='192.168.1.101'
        )
        
        self.assertEqual(response.status_code, 201)
        response_data = response.json()
        self.assertIn('device_id', response_data)

    @patch('oauth2_auth.email_service.email_service.send_registration_activation')
    @patch('oauth2_auth.authentication.DeviceAuthenticationService.register_device')
    def test_registration_generates_device_id_if_not_provided(self, mock_register_device, mock_send_email):
        """Test registration generates device_id if not provided"""
        mock_register_device.return_value = True
        mock_send_email.return_value = True
        
        registration_data = {
            'name': 'Test User',
            'email': '<EMAIL>',
            'password': 'testpass123',
            # No device_id provided - should be auto-generated
        }
        
        response = self.client.post(
            '/user/register/',
            data=json.dumps(registration_data),
            content_type='application/json',
            HTTP_USER_AGENT='Mozilla/5.0 Test Browser',
            REMOTE_ADDR='192.168.1.101'
        )
        
        self.assertEqual(response.status_code, 201)
        response_data = response.json()
        self.assertIn('device_id', response_data)
        # Generated device_id should be at least 58 characters
        self.assertGreaterEqual(len(response_data['device_id']), 58)

    def tearDown(self):
        """Clean up test data"""
        cache.clear()
        User.objects.all().delete()
        DeviceToken.objects.all().delete()
