"""
Test cases for device validation during registration
"""

import pytest
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.core.cache import cache
from unittest.mock import patch, MagicMock

from oauth2_auth.models import DeviceToken
from oauth2_auth.authentication import DeviceAuthenticationService
from user.views import register
from agritram.exceptions import ValidationException

User = get_user_model()


class DeviceValidationRegistrationTest(TestCase):
    """Test device validation during user registration"""

    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()
        cache.clear()

        # Create a test user with existing device
        self.existing_user = User.objects.create_user(
            email="<EMAIL>", name="Existing User", password="testpass123"
        )

        # Create an existing device
        self.existing_device = DeviceToken.objects.create(
            user=self.existing_user,
            device_id="existing_device_12345678901234567890123456789012345678901234567890",
            device_name="Existing Device",
            device_type="web",
            fingerprint="existing_fingerprint",
            ip_address="*************",
            user_agent="Mozilla/5.0 Test Browser",
        )

    def test_device_validation_service_new_device(self):
        """Test device validation service with new device"""
        request = self.factory.post(
            "/register/",
            {
                "device_id": "new_device_12345678901234567890123456789012345678901234567890"
            },
        )
        request.META["REMOTE_ADDR"] = "*************"
        request.META["HTTP_USER_AGENT"] = "Mozilla/5.0 New Browser"

        result = DeviceAuthenticationService.validate_device_for_registration(
            device_id="new_device_12345678901234567890123456789012345678901234567890",
            device_type="web",
            request=request,
        )

        self.assertTrue(result["is_valid"])
        self.assertEqual(result["message"], "Device validation passed")
        self.assertTrue(result["details"]["is_new_device"])
        self.assertEqual(len(result["warnings"]), 0)

    def test_device_validation_service_existing_device_same_type(self):
        """Test device validation service with existing device and same type"""
        request = self.factory.post(
            "/register/", {"device_id": self.existing_device.device_id}
        )
        request.META["REMOTE_ADDR"] = "*************"  # Same IP
        request.META["HTTP_USER_AGENT"] = "Mozilla/5.0 Test Browser"  # Same UA

        with patch("oauth2_auth.utils.generate_device_fingerprint") as mock_fingerprint:
            mock_fingerprint.return_value = "existing_fingerprint"  # Same fingerprint

            result = DeviceAuthenticationService.validate_device_for_registration(
                device_id=self.existing_device.device_id,
                device_type="web",  # Same type
                request=request,
            )

        self.assertTrue(result["is_valid"])
        self.assertEqual(result["message"], "Device validation passed")
        self.assertFalse(result["details"].get("is_new_device", False))
        self.assertEqual(len(result["warnings"]), 0)

    def test_device_validation_service_existing_device_different_type(self):
        """Test device validation service with existing device but different type"""
        request = self.factory.post(
            "/register/", {"device_id": self.existing_device.device_id}
        )
        request.META["REMOTE_ADDR"] = "*************"
        request.META["HTTP_USER_AGENT"] = "Mozilla/5.0 Test Browser"

        result = DeviceAuthenticationService.validate_device_for_registration(
            device_id=self.existing_device.device_id,
            device_type="mobile",  # Different type
            request=request,
        )

        self.assertFalse(result["is_valid"])
        self.assertEqual(result["message"], "Device type mismatch")
        self.assertIn("device_type_mismatch", result["details"])
        self.assertEqual(result["details"]["device_type_mismatch"]["stored"], "web")
        self.assertEqual(
            result["details"]["device_type_mismatch"]["provided"], "mobile"
        )

    def test_device_validation_service_existing_device_ip_change(self):
        """Test device validation service with existing device but different IP"""
        request = self.factory.post(
            "/register/", {"device_id": self.existing_device.device_id}
        )
        request.META["REMOTE_ADDR"] = "*************"  # Different IP
        request.META["HTTP_USER_AGENT"] = "Mozilla/5.0 Test Browser"

        with patch("oauth2_auth.utils.generate_device_fingerprint") as mock_fingerprint:
            mock_fingerprint.return_value = "existing_fingerprint"

            result = DeviceAuthenticationService.validate_device_for_registration(
                device_id=self.existing_device.device_id,
                device_type="web",
                request=request,
            )

        self.assertTrue(result["is_valid"])  # IP change is warning only
        self.assertIn("ip_address_changed", result["warnings"])
        self.assertIn("ip_change", result["details"])
        self.assertEqual(result["details"]["ip_change"]["stored"], "*************")
        self.assertEqual(result["details"]["ip_change"]["current"], "*************")

    def test_registration_with_valid_device_type(self):
        """Test registration with valid device_type parameter"""
        request_data = {
            "name": "Test User",
            "email": "<EMAIL>",
            "password": "testpass123",
            "device_id": "test_device_12345678901234567890123456789012345678901234567890",
            "device_type": "web",
        }

        request = self.factory.post("/register/", request_data)
        request.META["REMOTE_ADDR"] = "*************"
        request.META["HTTP_USER_AGENT"] = "Mozilla/5.0 Test Browser"
        request.data = request_data

        with patch(
            "oauth2_auth.email_service.email_service.send_registration_activation"
        ) as mock_email:
            with patch(
                "oauth2_auth.authentication.DeviceAuthenticationService.register_device"
            ) as mock_register:
                mock_register.return_value = True

                response = register(request)

                self.assertEqual(response.status_code, 201)
                self.assertIn("device_id", response.data)

    def test_registration_with_invalid_device_type(self):
        """Test registration with invalid device_type parameter"""
        request_data = {
            "name": "Test User",
            "email": "<EMAIL>",
            "password": "testpass123",
            "device_id": "test_device_12345678901234567890123456789012345678901234567890",
            "device_type": "invalid_type",
        }

        request = self.factory.post("/register/", request_data)
        request.META["REMOTE_ADDR"] = "*************"
        request.META["HTTP_USER_AGENT"] = "Mozilla/5.0 Test Browser"
        request.data = request_data

        with pytest.raises(ValidationException) as exc_info:
            register(request)

        self.assertIn("Invalid device type", str(exc_info.value))

    def test_registration_with_existing_device_type_mismatch(self):
        """Test registration with existing device but mismatched type"""
        request_data = {
            "name": "Test User",
            "email": "<EMAIL>",
            "password": "testpass123",
            "device_id": self.existing_device.device_id,
            "device_type": "mobile",  # Different from stored 'web'
        }

        request = self.factory.post("/register/", request_data)
        request.META["REMOTE_ADDR"] = "*************"
        request.META["HTTP_USER_AGENT"] = "Mozilla/5.0 Test Browser"
        request.data = request_data

        with pytest.raises(ValidationException) as exc_info:
            register(request)

        self.assertIn("Device validation failed", str(exc_info.value))

    def test_registration_device_type_detection_override(self):
        """Test that provided device_type overrides detected type with warning"""
        request_data = {
            "name": "Test User",
            "email": "<EMAIL>",
            "password": "testpass123",
            "device_id": "test_device_12345678901234567890123456789012345678901234567890",
            "device_type": "mobile",  # Provided type
        }

        request = self.factory.post("/register/", request_data)
        request.META["REMOTE_ADDR"] = "*************"
        request.META["HTTP_USER_AGENT"] = (
            "Mozilla/5.0 Desktop Browser"  # Would detect as 'web'
        )
        request.data = request_data

        with patch(
            "oauth2_auth.email_service.email_service.send_registration_activation"
        ) as mock_email:
            with patch(
                "oauth2_auth.authentication.DeviceAuthenticationService.register_device"
            ) as mock_register:
                with patch("agritram.logger_utils.log_operation_info") as mock_log:
                    mock_register.return_value = True

                    response = register(request)

                    self.assertEqual(response.status_code, 201)

                    # Check that device type mismatch was logged
                    log_calls = [
                        call
                        for call in mock_log.call_args_list
                        if len(call[0]) > 1 and "DEVICE_TYPE_MISMATCH" in call[0][1]
                    ]
                    self.assertTrue(len(log_calls) > 0)

    def tearDown(self):
        """Clean up test data"""
        cache.clear()
        User.objects.all().delete()
        DeviceToken.objects.all().delete()
