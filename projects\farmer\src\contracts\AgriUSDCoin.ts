/* eslint-disable */
/**
 * This file was automatically generated by @algorandfoundation/algokit-client-generator.
 * DO NOT MODIFY IT BY HAND.
 * requires: @algorandfoundation/algokit-utils: ^7
 */
import { type AlgorandClient } from '@algorandfoundation/algokit-utils/types/algorand-client'
import { ABIReturn, AppReturn, SendAppTransactionResult } from '@algorandfoundation/algokit-utils/types/app'
import { Arc56Contract, getArc56ReturnValue, getABIStructFromABITuple } from '@algorandfoundation/algokit-utils/types/app-arc56'
import {
  AppClient as _AppClient,
  AppClientMethodCallParams,
  AppClientParams,
  AppClientBareCallParams,
  CallOnComplete,
  AppClientCompilationParams,
  ResolveAppClientByCreatorAndName,
  ResolveAppClientByNetwork,
  CloneAppClientParams,
} from '@algorandfoundation/algokit-utils/types/app-client'
import { AppFactory as _AppFactory, AppFactoryAppClientParams, AppFactoryResolveAppClientByCreatorAndNameParams, AppFactoryDeployParams, AppFactoryParams, CreateSchema } from '@algorandfoundation/algokit-utils/types/app-factory'
import { TransactionComposer, AppCallMethodCall, AppMethodCallTransactionArgument, SimulateOptions, RawSimulateOptions, SkipSignaturesSimulateOptions } from '@algorandfoundation/algokit-utils/types/composer'
import { SendParams, SendSingleTransactionResult, SendAtomicTransactionComposerResults } from '@algorandfoundation/algokit-utils/types/transaction'
import { Address, encodeAddress, modelsv2, OnApplicationComplete, Transaction, TransactionSigner } from 'algosdk'
import SimulateResponse = modelsv2.SimulateResponse

export const APP_SPEC: Arc56Contract = {"name":"AgriUSDCoin","structs":{},"methods":[{"name":"create","args":[],"returns":{"type":"byte[]"},"actions":{"create":[],"call":["NoOp"]},"readonly":false,"desc":"Create the Agri USD Coin ASA.","events":[],"recommendations":{}},{"name":"get_asset_id","args":[],"returns":{"type":"uint64"},"actions":{"create":[],"call":["NoOp"]},"readonly":true,"events":[],"recommendations":{}},{"name":"get_minted_tokens","args":[],"returns":{"type":"uint64"},"actions":{"create":[],"call":["NoOp"]},"readonly":true,"events":[],"recommendations":{}},{"name":"get_burnt_tokens","args":[],"returns":{"type":"uint64"},"actions":{"create":[],"call":["NoOp"]},"readonly":true,"events":[],"recommendations":{}},{"name":"mint_tokens","args":[{"type":"uint64","name":"amount"},{"type":"account","name":"receiver"}],"returns":{"type":"byte[]"},"actions":{"create":[],"call":["NoOp"]},"readonly":false,"events":[],"recommendations":{}},{"name":"burn_tokens","args":[{"type":"uint64","name":"amount"},{"type":"account","name":"address"}],"returns":{"type":"byte[]"},"actions":{"create":[],"call":["NoOp"]},"readonly":false,"events":[],"recommendations":{}},{"name":"transfer_tokens","args":[{"type":"uint64","name":"amount"},{"type":"account","name":"receiver"},{"type":"account","name":"account"}],"returns":{"type":"byte[]"},"actions":{"create":[],"call":["NoOp"]},"readonly":false,"events":[],"recommendations":{}}],"arcs":[22,28],"networks":{},"state":{"schema":{"global":{"ints":3,"bytes":0},"local":{"ints":0,"bytes":0}},"keys":{"global":{"minted_tokens":{"keyType":"AVMString","valueType":"AVMUint64","key":"bWludGVkX3Rva2Vucw=="},"burnt_tokens":{"keyType":"AVMString","valueType":"AVMUint64","key":"YnVybnRfdG9rZW5z"},"asset":{"keyType":"AVMString","valueType":"AVMUint64","key":"YXNzZXQ="}},"local":{},"box":{}},"maps":{"global":{},"local":{},"box":{}}},"bareActions":{"create":["NoOp"],"call":[]},"sourceInfo":{"approval":{"sourceInfo":[{"pc":[325],"errorMessage":"ASA already created"},{"pc":[126,165,198,231,248,265,282],"errorMessage":"OnCompletion is not NoOp"},{"pc":[319],"errorMessage":"Only the creator can create the ASA"},{"pc":[311],"errorMessage":"can only call when creating"},{"pc":[129,168,201,234,251,268,285],"errorMessage":"can only call when not creating"},{"pc":[323,404,425,470,516],"errorMessage":"check self.asset exists"},{"pc":[414,499],"errorMessage":"check self.burnt_tokens exists"},{"pc":[409,451,546],"errorMessage":"check self.minted_tokens exists"}],"pcOffsetMethod":"none"},"clear":{"sourceInfo":[],"pcOffsetMethod":"none"}},"source":{"approval":"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","clear":"I3ByYWdtYSB2ZXJzaW9uIDEwCiNwcmFnbWEgdHlwZXRyYWNrIGZhbHNlCgovLyBhbGdvcHkuYXJjNC5BUkM0Q29udHJhY3QuY2xlYXJfc3RhdGVfcHJvZ3JhbSgpIC0+IHVpbnQ2NDoKbWFpbjoKICAgIHB1c2hpbnQgMSAvLyAxCiAgICByZXR1cm4K"},"byteCode":{"approval":"CiADAAEEJgQFYXNzZXQEFR98dQ1taW50ZWRfdG9rZW5zDGJ1cm50X3Rva2VuczEYQAAJKiJnKyJnKCJnMRtBAO6CBwSCE63mBFuiKoQEUo+dywSamI+VBHrKjfoELZoN5wTDcvl5NhoAjgcAngCNAHwAawBKACkAAiJDMRkURDEYRDYaATYaAhfAHDYaAxfAHIgBZ0kVFlcGAkxQKUxQsCNDMRkURDEYRDYaATYaAhfAHIgBFkkVFlcGAkxQKUxQsCNDMRkURDEYRDYaATYaAhfAHIgAykkVFlcGAkxQKUxQsCNDMRkURDEYRIgArRYpTFCwI0MxGRREMRhEiACXFilMULAjQzEZFEQxGESIAIEWKUxQsCNDMRkURDEYRIgAGUkVFlcGAkxQKUxQsCNDMRlA/0UxGBREI0MxADIJEkQiKGVEFESxMgAyCkcDsiyyK7IqsimACEFHUkktVVNEsiWADUFncmkgVVNEIENvaW6yJoECsiOB////////////AbIigQOyELIBs7QXKLQ8Z4kiKGVEiSIqZUSJIitlRImKAgGxMgAiKGVEi/4XSbISTLIRi/+yFCSyEEyyAbO0FyIqZURPAggqTGeJigIBsTIAMgoiKGVEi/4Xi/+yE0myEkyyEUyyFCSyEEyyAbO0FyIrZURPAggrTGeJigMBsTIAIihlRIv9F4v/shNJshJMshGL/rIUJLIQTLIBs7QXIiplRE8CCCpMZ4k=","clear":"CoEBQw=="},"compilerInfo":{"compiler":"puya","compilerVersion":{"major":4,"minor":7,"patch":0}},"events":[],"templateVariables":{}} as unknown as Arc56Contract

/**
 * A state record containing binary data
 */
export interface BinaryState {
  /**
   * Gets the state value as a Uint8Array
   */
  asByteArray(): Uint8Array | undefined
  /**
   * Gets the state value as a string
   */
  asString(): string | undefined
}

class BinaryStateValue implements BinaryState {
  constructor(private value: Uint8Array | undefined) {}

  asByteArray(): Uint8Array | undefined {
    return this.value
  }

  asString(): string | undefined {
    return this.value !== undefined ? Buffer.from(this.value).toString('utf-8') : undefined
  }
}

/**
 * Expands types for IntelliSense so they are more human readable
 * See https://stackoverflow.com/a/69288824
 */
export type Expand<T> = T extends (...args: infer A) => infer R
  ? (...args: Expand<A>) => Expand<R>
  : T extends infer O
    ? { [K in keyof O]: O[K] }
    : never


/**
 * The argument types for the AgriUsdCoin contract
 */
export type AgriUsdCoinArgs = {
  /**
   * The object representation of the arguments for each method
   */
  obj: {
    'create()byte[]': Record<string, never>
    'get_asset_id()uint64': Record<string, never>
    'get_minted_tokens()uint64': Record<string, never>
    'get_burnt_tokens()uint64': Record<string, never>
    'mint_tokens(uint64,account)byte[]': {
      amount: bigint | number
      receiver: Uint8Array | string
    }
    'burn_tokens(uint64,account)byte[]': {
      amount: bigint | number
      address: Uint8Array | string
    }
    'transfer_tokens(uint64,account,account)byte[]': {
      amount: bigint | number
      receiver: Uint8Array | string
      account: Uint8Array | string
    }
  }
  /**
   * The tuple representation of the arguments for each method
   */
  tuple: {
    'create()byte[]': []
    'get_asset_id()uint64': []
    'get_minted_tokens()uint64': []
    'get_burnt_tokens()uint64': []
    'mint_tokens(uint64,account)byte[]': [amount: bigint | number, receiver: Uint8Array | string]
    'burn_tokens(uint64,account)byte[]': [amount: bigint | number, address: Uint8Array | string]
    'transfer_tokens(uint64,account,account)byte[]': [amount: bigint | number, receiver: Uint8Array | string, account: Uint8Array | string]
  }
}

/**
 * The return type for each method
 */
export type AgriUsdCoinReturns = {
  'create()byte[]': Uint8Array
  'get_asset_id()uint64': bigint
  'get_minted_tokens()uint64': bigint
  'get_burnt_tokens()uint64': bigint
  'mint_tokens(uint64,account)byte[]': Uint8Array
  'burn_tokens(uint64,account)byte[]': Uint8Array
  'transfer_tokens(uint64,account,account)byte[]': Uint8Array
}

/**
 * Defines the types of available calls and state of the AgriUsdCoin smart contract.
 */
export type AgriUsdCoinTypes = {
  /**
   * Maps method signatures / names to their argument and return types.
   */
  methods:
    & Record<'create()byte[]' | 'create', {
      argsObj: AgriUsdCoinArgs['obj']['create()byte[]']
      argsTuple: AgriUsdCoinArgs['tuple']['create()byte[]']
      returns: AgriUsdCoinReturns['create()byte[]']
    }>
    & Record<'get_asset_id()uint64' | 'get_asset_id', {
      argsObj: AgriUsdCoinArgs['obj']['get_asset_id()uint64']
      argsTuple: AgriUsdCoinArgs['tuple']['get_asset_id()uint64']
      returns: AgriUsdCoinReturns['get_asset_id()uint64']
    }>
    & Record<'get_minted_tokens()uint64' | 'get_minted_tokens', {
      argsObj: AgriUsdCoinArgs['obj']['get_minted_tokens()uint64']
      argsTuple: AgriUsdCoinArgs['tuple']['get_minted_tokens()uint64']
      returns: AgriUsdCoinReturns['get_minted_tokens()uint64']
    }>
    & Record<'get_burnt_tokens()uint64' | 'get_burnt_tokens', {
      argsObj: AgriUsdCoinArgs['obj']['get_burnt_tokens()uint64']
      argsTuple: AgriUsdCoinArgs['tuple']['get_burnt_tokens()uint64']
      returns: AgriUsdCoinReturns['get_burnt_tokens()uint64']
    }>
    & Record<'mint_tokens(uint64,account)byte[]' | 'mint_tokens', {
      argsObj: AgriUsdCoinArgs['obj']['mint_tokens(uint64,account)byte[]']
      argsTuple: AgriUsdCoinArgs['tuple']['mint_tokens(uint64,account)byte[]']
      returns: AgriUsdCoinReturns['mint_tokens(uint64,account)byte[]']
    }>
    & Record<'burn_tokens(uint64,account)byte[]' | 'burn_tokens', {
      argsObj: AgriUsdCoinArgs['obj']['burn_tokens(uint64,account)byte[]']
      argsTuple: AgriUsdCoinArgs['tuple']['burn_tokens(uint64,account)byte[]']
      returns: AgriUsdCoinReturns['burn_tokens(uint64,account)byte[]']
    }>
    & Record<'transfer_tokens(uint64,account,account)byte[]' | 'transfer_tokens', {
      argsObj: AgriUsdCoinArgs['obj']['transfer_tokens(uint64,account,account)byte[]']
      argsTuple: AgriUsdCoinArgs['tuple']['transfer_tokens(uint64,account,account)byte[]']
      returns: AgriUsdCoinReturns['transfer_tokens(uint64,account,account)byte[]']
    }>
  /**
   * Defines the shape of the state of the application.
   */
  state: {
    global: {
      keys: {
        mintedTokens: bigint
        burntTokens: bigint
        asset: bigint
      }
      maps: {}
    }
  }
}

/**
 * Defines the possible abi call signatures.
 */
export type AgriUsdCoinSignatures = keyof AgriUsdCoinTypes['methods']
/**
 * Defines the possible abi call signatures for methods that return a non-void value.
 */
export type AgriUsdCoinNonVoidMethodSignatures = keyof AgriUsdCoinTypes['methods'] extends infer T ? T extends keyof AgriUsdCoinTypes['methods'] ? MethodReturn<T> extends void ? never : T  : never : never
/**
 * Defines an object containing all relevant parameters for a single call to the contract.
 */
export type CallParams<TArgs> = Expand<
  Omit<AppClientMethodCallParams, 'method' | 'args' | 'onComplete'> &
    {
      /** The args for the ABI method call, either as an ordered array or an object */
      args: Expand<TArgs>
    }
>
/**
 * Maps a method signature from the AgriUsdCoin smart contract to the method's arguments in either tuple or struct form
 */
export type MethodArgs<TSignature extends AgriUsdCoinSignatures> = AgriUsdCoinTypes['methods'][TSignature]['argsObj' | 'argsTuple']
/**
 * Maps a method signature from the AgriUsdCoin smart contract to the method's return type
 */
export type MethodReturn<TSignature extends AgriUsdCoinSignatures> = AgriUsdCoinTypes['methods'][TSignature]['returns']

/**
 * Defines the shape of the keyed global state of the application.
 */
export type GlobalKeysState = AgriUsdCoinTypes['state']['global']['keys']


/**
 * Defines supported create method params for this smart contract
 */
export type AgriUsdCoinCreateCallParams =
  | Expand<AppClientBareCallParams & {method?: never} & {onComplete?: OnApplicationComplete.NoOpOC} & CreateSchema>
/**
 * Defines arguments required for the deploy method.
 */
export type AgriUsdCoinDeployParams = Expand<Omit<AppFactoryDeployParams, 'createParams' | 'updateParams' | 'deleteParams'> & {
  /**
   * Create transaction parameters to use if a create needs to be issued as part of deployment; use `method` to define ABI call (if available) or leave out for a bare call (if available)
   */
  createParams?: AgriUsdCoinCreateCallParams
}>


/**
 * Exposes methods for constructing `AppClient` params objects for ABI calls to the AgriUsdCoin smart contract
 */
export abstract class AgriUsdCoinParamsFactory {
  /**
   * Constructs a no op call for the create()byte[] ABI method
   *
   * Create the Agri USD Coin ASA.
   *
   * @param params Parameters for the call
   * @returns An `AppClientMethodCallParams` object for the call
   */
  static create(params: CallParams<AgriUsdCoinArgs['obj']['create()byte[]'] | AgriUsdCoinArgs['tuple']['create()byte[]']> & CallOnComplete): AppClientMethodCallParams & CallOnComplete {
    return {
      ...params,
      method: 'create()byte[]' as const,
      args: Array.isArray(params.args) ? params.args : [],
    }
  }
  /**
   * Constructs a no op call for the get_asset_id()uint64 ABI method
   *
   * @param params Parameters for the call
   * @returns An `AppClientMethodCallParams` object for the call
   */
  static getAssetId(params: CallParams<AgriUsdCoinArgs['obj']['get_asset_id()uint64'] | AgriUsdCoinArgs['tuple']['get_asset_id()uint64']> & CallOnComplete): AppClientMethodCallParams & CallOnComplete {
    return {
      ...params,
      method: 'get_asset_id()uint64' as const,
      args: Array.isArray(params.args) ? params.args : [],
    }
  }
  /**
   * Constructs a no op call for the get_minted_tokens()uint64 ABI method
   *
   * @param params Parameters for the call
   * @returns An `AppClientMethodCallParams` object for the call
   */
  static getMintedTokens(params: CallParams<AgriUsdCoinArgs['obj']['get_minted_tokens()uint64'] | AgriUsdCoinArgs['tuple']['get_minted_tokens()uint64']> & CallOnComplete): AppClientMethodCallParams & CallOnComplete {
    return {
      ...params,
      method: 'get_minted_tokens()uint64' as const,
      args: Array.isArray(params.args) ? params.args : [],
    }
  }
  /**
   * Constructs a no op call for the get_burnt_tokens()uint64 ABI method
   *
   * @param params Parameters for the call
   * @returns An `AppClientMethodCallParams` object for the call
   */
  static getBurntTokens(params: CallParams<AgriUsdCoinArgs['obj']['get_burnt_tokens()uint64'] | AgriUsdCoinArgs['tuple']['get_burnt_tokens()uint64']> & CallOnComplete): AppClientMethodCallParams & CallOnComplete {
    return {
      ...params,
      method: 'get_burnt_tokens()uint64' as const,
      args: Array.isArray(params.args) ? params.args : [],
    }
  }
  /**
   * Constructs a no op call for the mint_tokens(uint64,account)byte[] ABI method
   *
   * @param params Parameters for the call
   * @returns An `AppClientMethodCallParams` object for the call
   */
  static mintTokens(params: CallParams<AgriUsdCoinArgs['obj']['mint_tokens(uint64,account)byte[]'] | AgriUsdCoinArgs['tuple']['mint_tokens(uint64,account)byte[]']> & CallOnComplete): AppClientMethodCallParams & CallOnComplete {
    return {
      ...params,
      method: 'mint_tokens(uint64,account)byte[]' as const,
      args: Array.isArray(params.args) ? params.args : [params.args.amount, params.args.receiver],
    }
  }
  /**
   * Constructs a no op call for the burn_tokens(uint64,account)byte[] ABI method
   *
   * @param params Parameters for the call
   * @returns An `AppClientMethodCallParams` object for the call
   */
  static burnTokens(params: CallParams<AgriUsdCoinArgs['obj']['burn_tokens(uint64,account)byte[]'] | AgriUsdCoinArgs['tuple']['burn_tokens(uint64,account)byte[]']> & CallOnComplete): AppClientMethodCallParams & CallOnComplete {
    return {
      ...params,
      method: 'burn_tokens(uint64,account)byte[]' as const,
      args: Array.isArray(params.args) ? params.args : [params.args.amount, params.args.address],
    }
  }
  /**
   * Constructs a no op call for the transfer_tokens(uint64,account,account)byte[] ABI method
   *
   * @param params Parameters for the call
   * @returns An `AppClientMethodCallParams` object for the call
   */
  static transferTokens(params: CallParams<AgriUsdCoinArgs['obj']['transfer_tokens(uint64,account,account)byte[]'] | AgriUsdCoinArgs['tuple']['transfer_tokens(uint64,account,account)byte[]']> & CallOnComplete): AppClientMethodCallParams & CallOnComplete {
    return {
      ...params,
      method: 'transfer_tokens(uint64,account,account)byte[]' as const,
      args: Array.isArray(params.args) ? params.args : [params.args.amount, params.args.receiver, params.args.account],
    }
  }
}

/**
 * A factory to create and deploy one or more instance of the AgriUSDCoin smart contract and to create one or more app clients to interact with those (or other) app instances
 */
export class AgriUsdCoinFactory {
  /**
   * The underlying `AppFactory` for when you want to have more flexibility
   */
  public readonly appFactory: _AppFactory

  /**
   * Creates a new instance of `AgriUsdCoinFactory`
   *
   * @param params The parameters to initialise the app factory with
   */
  constructor(params: Omit<AppFactoryParams, 'appSpec'>) {
    this.appFactory = new _AppFactory({
      ...params,
      appSpec: APP_SPEC,
    })
  }
  
  /** The name of the app (from the ARC-32 / ARC-56 app spec or override). */
  public get appName() {
    return this.appFactory.appName
  }
  
  /** The ARC-56 app spec being used */
  get appSpec() {
    return APP_SPEC
  }
  
  /** A reference to the underlying `AlgorandClient` this app factory is using. */
  public get algorand(): AlgorandClient {
    return this.appFactory.algorand
  }
  
  /**
   * Returns a new `AppClient` client for an app instance of the given ID.
   *
   * Automatically populates appName, defaultSender and source maps from the factory
   * if not specified in the params.
   * @param params The parameters to create the app client
   * @returns The `AppClient`
   */
  public getAppClientById(params: AppFactoryAppClientParams) {
    return new AgriUsdCoinClient(this.appFactory.getAppClientById(params))
  }
  
  /**
   * Returns a new `AppClient` client, resolving the app by creator address and name
   * using AlgoKit app deployment semantics (i.e. looking for the app creation transaction note).
   *
   * Automatically populates appName, defaultSender and source maps from the factory
   * if not specified in the params.
   * @param params The parameters to create the app client
   * @returns The `AppClient`
   */
  public async getAppClientByCreatorAndName(
    params: AppFactoryResolveAppClientByCreatorAndNameParams,
  ) {
    return new AgriUsdCoinClient(await this.appFactory.getAppClientByCreatorAndName(params))
  }

  /**
   * Idempotently deploys the AgriUSDCoin smart contract.
   *
   * @param params The arguments for the contract calls and any additional parameters for the call
   * @returns The deployment result
   */
  public async deploy(params: AgriUsdCoinDeployParams = {}) {
    const result = await this.appFactory.deploy({
      ...params,
    })
    return { result: result.result, appClient: new AgriUsdCoinClient(result.appClient) }
  }

  /**
   * Get parameters to create transactions (create and deploy related calls) for the current app. A good mental model for this is that these parameters represent a deferred transaction creation.
   */
  readonly params = {
    /**
     * Gets available create methods
     */
    create: {
      /**
       * Creates a new instance of the AgriUSDCoin smart contract using a bare call.
       *
       * @param params The params for the bare (raw) call
       * @returns The params for a create call
       */
      bare: (params?: Expand<AppClientBareCallParams & AppClientCompilationParams & CreateSchema & {onComplete?: OnApplicationComplete.NoOpOC}>) => {
        return this.appFactory.params.bare.create(params)
      },
    },

  }

  /**
   * Create transactions for the current app
   */
  readonly createTransaction = {
    /**
     * Gets available create methods
     */
    create: {
      /**
       * Creates a new instance of the AgriUSDCoin smart contract using a bare call.
       *
       * @param params The params for the bare (raw) call
       * @returns The transaction for a create call
       */
      bare: (params?: Expand<AppClientBareCallParams & AppClientCompilationParams & CreateSchema & {onComplete?: OnApplicationComplete.NoOpOC}>) => {
        return this.appFactory.createTransaction.bare.create(params)
      },
    },

  }

  /**
   * Send calls to the current app
   */
  readonly send = {
    /**
     * Gets available create methods
     */
    create: {
      /**
       * Creates a new instance of the AgriUSDCoin smart contract using a bare call.
       *
       * @param params The params for the bare (raw) call
       * @returns The create result
       */
      bare: async (params?: Expand<AppClientBareCallParams & AppClientCompilationParams & CreateSchema & SendParams & {onComplete?: OnApplicationComplete.NoOpOC}>) => {
        const result = await this.appFactory.send.bare.create(params)
        return { result: result.result, appClient: new AgriUsdCoinClient(result.appClient) }
      },
    },

  }

}
/**
 * A client to make calls to the AgriUSDCoin smart contract
 */
export class AgriUsdCoinClient {
  /**
   * The underlying `AppClient` for when you want to have more flexibility
   */
  public readonly appClient: _AppClient

  /**
   * Creates a new instance of `AgriUsdCoinClient`
   *
   * @param appClient An `AppClient` instance which has been created with the AgriUsdCoin app spec
   */
  constructor(appClient: _AppClient)
  /**
   * Creates a new instance of `AgriUsdCoinClient`
   *
   * @param params The parameters to initialise the app client with
   */
  constructor(params: Omit<AppClientParams, 'appSpec'>)
  constructor(appClientOrParams: _AppClient | Omit<AppClientParams, 'appSpec'>) {
    this.appClient = appClientOrParams instanceof _AppClient ? appClientOrParams : new _AppClient({
      ...appClientOrParams,
      appSpec: APP_SPEC,
    })
  }
  
  /**
   * Checks for decode errors on the given return value and maps the return value to the return type for the given method
   * @returns The typed return value or undefined if there was no value
   */
  decodeReturnValue<TSignature extends AgriUsdCoinNonVoidMethodSignatures>(method: TSignature, returnValue: ABIReturn | undefined) {
    return returnValue !== undefined ? getArc56ReturnValue<MethodReturn<TSignature>>(returnValue, this.appClient.getABIMethod(method), APP_SPEC.structs) : undefined
  }
  
  /**
   * Returns a new `AgriUsdCoinClient` client, resolving the app by creator address and name
   * using AlgoKit app deployment semantics (i.e. looking for the app creation transaction note).
   * @param params The parameters to create the app client
   */
  public static async fromCreatorAndName(params: Omit<ResolveAppClientByCreatorAndName, 'appSpec'>): Promise<AgriUsdCoinClient> {
    return new AgriUsdCoinClient(await _AppClient.fromCreatorAndName({...params, appSpec: APP_SPEC}))
  }
  
  /**
   * Returns an `AgriUsdCoinClient` instance for the current network based on
   * pre-determined network-specific app IDs specified in the ARC-56 app spec.
   *
   * If no IDs are in the app spec or the network isn't recognised, an error is thrown.
   * @param params The parameters to create the app client
   */
  static async fromNetwork(
    params: Omit<ResolveAppClientByNetwork, 'appSpec'>
  ): Promise<AgriUsdCoinClient> {
    return new AgriUsdCoinClient(await _AppClient.fromNetwork({...params, appSpec: APP_SPEC}))
  }
  
  /** The ID of the app instance this client is linked to. */
  public get appId() {
    return this.appClient.appId
  }
  
  /** The app address of the app instance this client is linked to. */
  public get appAddress() {
    return this.appClient.appAddress
  }
  
  /** The name of the app. */
  public get appName() {
    return this.appClient.appName
  }
  
  /** The ARC-56 app spec being used */
  public get appSpec() {
    return this.appClient.appSpec
  }
  
  /** A reference to the underlying `AlgorandClient` this app client is using. */
  public get algorand(): AlgorandClient {
    return this.appClient.algorand
  }

  /**
   * Get parameters to create transactions for the current app. A good mental model for this is that these parameters represent a deferred transaction creation.
   */
  readonly params = {
    /**
     * Makes a clear_state call to an existing instance of the AgriUSDCoin smart contract.
     *
     * @param params The params for the bare (raw) call
     * @returns The clearState result
     */
    clearState: (params?: Expand<AppClientBareCallParams>) => {
      return this.appClient.params.bare.clearState(params)
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `create()byte[]` ABI method.
     *
     * Create the Agri USD Coin ASA.
     *
     * @param params The params for the smart contract call
     * @returns The call params
     */
    create: (params: CallParams<AgriUsdCoinArgs['obj']['create()byte[]'] | AgriUsdCoinArgs['tuple']['create()byte[]']> & {onComplete?: OnApplicationComplete.NoOpOC} = {args: []}) => {
      return this.appClient.params.call(AgriUsdCoinParamsFactory.create(params))
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `get_asset_id()uint64` ABI method.
     * 
     * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
     *
     * @param params The params for the smart contract call
     * @returns The call params
     */
    getAssetId: (params: CallParams<AgriUsdCoinArgs['obj']['get_asset_id()uint64'] | AgriUsdCoinArgs['tuple']['get_asset_id()uint64']> & {onComplete?: OnApplicationComplete.NoOpOC} = {args: []}) => {
      return this.appClient.params.call(AgriUsdCoinParamsFactory.getAssetId(params))
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `get_minted_tokens()uint64` ABI method.
     * 
     * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
     *
     * @param params The params for the smart contract call
     * @returns The call params
     */
    getMintedTokens: (params: CallParams<AgriUsdCoinArgs['obj']['get_minted_tokens()uint64'] | AgriUsdCoinArgs['tuple']['get_minted_tokens()uint64']> & {onComplete?: OnApplicationComplete.NoOpOC} = {args: []}) => {
      return this.appClient.params.call(AgriUsdCoinParamsFactory.getMintedTokens(params))
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `get_burnt_tokens()uint64` ABI method.
     * 
     * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
     *
     * @param params The params for the smart contract call
     * @returns The call params
     */
    getBurntTokens: (params: CallParams<AgriUsdCoinArgs['obj']['get_burnt_tokens()uint64'] | AgriUsdCoinArgs['tuple']['get_burnt_tokens()uint64']> & {onComplete?: OnApplicationComplete.NoOpOC} = {args: []}) => {
      return this.appClient.params.call(AgriUsdCoinParamsFactory.getBurntTokens(params))
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `mint_tokens(uint64,account)byte[]` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call params
     */
    mintTokens: (params: CallParams<AgriUsdCoinArgs['obj']['mint_tokens(uint64,account)byte[]'] | AgriUsdCoinArgs['tuple']['mint_tokens(uint64,account)byte[]']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.params.call(AgriUsdCoinParamsFactory.mintTokens(params))
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `burn_tokens(uint64,account)byte[]` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call params
     */
    burnTokens: (params: CallParams<AgriUsdCoinArgs['obj']['burn_tokens(uint64,account)byte[]'] | AgriUsdCoinArgs['tuple']['burn_tokens(uint64,account)byte[]']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.params.call(AgriUsdCoinParamsFactory.burnTokens(params))
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `transfer_tokens(uint64,account,account)byte[]` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call params
     */
    transferTokens: (params: CallParams<AgriUsdCoinArgs['obj']['transfer_tokens(uint64,account,account)byte[]'] | AgriUsdCoinArgs['tuple']['transfer_tokens(uint64,account,account)byte[]']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.params.call(AgriUsdCoinParamsFactory.transferTokens(params))
    },

  }

  /**
   * Create transactions for the current app
   */
  readonly createTransaction = {
    /**
     * Makes a clear_state call to an existing instance of the AgriUSDCoin smart contract.
     *
     * @param params The params for the bare (raw) call
     * @returns The clearState result
     */
    clearState: (params?: Expand<AppClientBareCallParams>) => {
      return this.appClient.createTransaction.bare.clearState(params)
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `create()byte[]` ABI method.
     *
     * Create the Agri USD Coin ASA.
     *
     * @param params The params for the smart contract call
     * @returns The call transaction
     */
    create: (params: CallParams<AgriUsdCoinArgs['obj']['create()byte[]'] | AgriUsdCoinArgs['tuple']['create()byte[]']> & {onComplete?: OnApplicationComplete.NoOpOC} = {args: []}) => {
      return this.appClient.createTransaction.call(AgriUsdCoinParamsFactory.create(params))
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `get_asset_id()uint64` ABI method.
     * 
     * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
     *
     * @param params The params for the smart contract call
     * @returns The call transaction
     */
    getAssetId: (params: CallParams<AgriUsdCoinArgs['obj']['get_asset_id()uint64'] | AgriUsdCoinArgs['tuple']['get_asset_id()uint64']> & {onComplete?: OnApplicationComplete.NoOpOC} = {args: []}) => {
      return this.appClient.createTransaction.call(AgriUsdCoinParamsFactory.getAssetId(params))
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `get_minted_tokens()uint64` ABI method.
     * 
     * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
     *
     * @param params The params for the smart contract call
     * @returns The call transaction
     */
    getMintedTokens: (params: CallParams<AgriUsdCoinArgs['obj']['get_minted_tokens()uint64'] | AgriUsdCoinArgs['tuple']['get_minted_tokens()uint64']> & {onComplete?: OnApplicationComplete.NoOpOC} = {args: []}) => {
      return this.appClient.createTransaction.call(AgriUsdCoinParamsFactory.getMintedTokens(params))
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `get_burnt_tokens()uint64` ABI method.
     * 
     * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
     *
     * @param params The params for the smart contract call
     * @returns The call transaction
     */
    getBurntTokens: (params: CallParams<AgriUsdCoinArgs['obj']['get_burnt_tokens()uint64'] | AgriUsdCoinArgs['tuple']['get_burnt_tokens()uint64']> & {onComplete?: OnApplicationComplete.NoOpOC} = {args: []}) => {
      return this.appClient.createTransaction.call(AgriUsdCoinParamsFactory.getBurntTokens(params))
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `mint_tokens(uint64,account)byte[]` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call transaction
     */
    mintTokens: (params: CallParams<AgriUsdCoinArgs['obj']['mint_tokens(uint64,account)byte[]'] | AgriUsdCoinArgs['tuple']['mint_tokens(uint64,account)byte[]']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.createTransaction.call(AgriUsdCoinParamsFactory.mintTokens(params))
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `burn_tokens(uint64,account)byte[]` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call transaction
     */
    burnTokens: (params: CallParams<AgriUsdCoinArgs['obj']['burn_tokens(uint64,account)byte[]'] | AgriUsdCoinArgs['tuple']['burn_tokens(uint64,account)byte[]']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.createTransaction.call(AgriUsdCoinParamsFactory.burnTokens(params))
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `transfer_tokens(uint64,account,account)byte[]` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call transaction
     */
    transferTokens: (params: CallParams<AgriUsdCoinArgs['obj']['transfer_tokens(uint64,account,account)byte[]'] | AgriUsdCoinArgs['tuple']['transfer_tokens(uint64,account,account)byte[]']> & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      return this.appClient.createTransaction.call(AgriUsdCoinParamsFactory.transferTokens(params))
    },

  }

  /**
   * Send calls to the current app
   */
  readonly send = {
    /**
     * Makes a clear_state call to an existing instance of the AgriUSDCoin smart contract.
     *
     * @param params The params for the bare (raw) call
     * @returns The clearState result
     */
    clearState: (params?: Expand<AppClientBareCallParams & SendParams>) => {
      return this.appClient.send.bare.clearState(params)
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `create()byte[]` ABI method.
     *
     * Create the Agri USD Coin ASA.
     *
     * @param params The params for the smart contract call
     * @returns The call result
     */
    create: async (params: CallParams<AgriUsdCoinArgs['obj']['create()byte[]'] | AgriUsdCoinArgs['tuple']['create()byte[]']> & SendParams & {onComplete?: OnApplicationComplete.NoOpOC} = {args: []}) => {
      const result = await this.appClient.send.call(AgriUsdCoinParamsFactory.create(params))
      return {...result, return: result.return as unknown as (undefined | AgriUsdCoinReturns['create()byte[]'])}
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `get_asset_id()uint64` ABI method.
     * 
     * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
     *
     * @param params The params for the smart contract call
     * @returns The call result
     */
    getAssetId: async (params: CallParams<AgriUsdCoinArgs['obj']['get_asset_id()uint64'] | AgriUsdCoinArgs['tuple']['get_asset_id()uint64']> & SendParams & {onComplete?: OnApplicationComplete.NoOpOC} = {args: []}) => {
      const result = await this.appClient.send.call(AgriUsdCoinParamsFactory.getAssetId(params))
      return {...result, return: result.return as unknown as (undefined | AgriUsdCoinReturns['get_asset_id()uint64'])}
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `get_minted_tokens()uint64` ABI method.
     * 
     * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
     *
     * @param params The params for the smart contract call
     * @returns The call result
     */
    getMintedTokens: async (params: CallParams<AgriUsdCoinArgs['obj']['get_minted_tokens()uint64'] | AgriUsdCoinArgs['tuple']['get_minted_tokens()uint64']> & SendParams & {onComplete?: OnApplicationComplete.NoOpOC} = {args: []}) => {
      const result = await this.appClient.send.call(AgriUsdCoinParamsFactory.getMintedTokens(params))
      return {...result, return: result.return as unknown as (undefined | AgriUsdCoinReturns['get_minted_tokens()uint64'])}
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `get_burnt_tokens()uint64` ABI method.
     * 
     * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
     *
     * @param params The params for the smart contract call
     * @returns The call result
     */
    getBurntTokens: async (params: CallParams<AgriUsdCoinArgs['obj']['get_burnt_tokens()uint64'] | AgriUsdCoinArgs['tuple']['get_burnt_tokens()uint64']> & SendParams & {onComplete?: OnApplicationComplete.NoOpOC} = {args: []}) => {
      const result = await this.appClient.send.call(AgriUsdCoinParamsFactory.getBurntTokens(params))
      return {...result, return: result.return as unknown as (undefined | AgriUsdCoinReturns['get_burnt_tokens()uint64'])}
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `mint_tokens(uint64,account)byte[]` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call result
     */
    mintTokens: async (params: CallParams<AgriUsdCoinArgs['obj']['mint_tokens(uint64,account)byte[]'] | AgriUsdCoinArgs['tuple']['mint_tokens(uint64,account)byte[]']> & SendParams & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      const result = await this.appClient.send.call(AgriUsdCoinParamsFactory.mintTokens(params))
      return {...result, return: result.return as unknown as (undefined | AgriUsdCoinReturns['mint_tokens(uint64,account)byte[]'])}
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `burn_tokens(uint64,account)byte[]` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call result
     */
    burnTokens: async (params: CallParams<AgriUsdCoinArgs['obj']['burn_tokens(uint64,account)byte[]'] | AgriUsdCoinArgs['tuple']['burn_tokens(uint64,account)byte[]']> & SendParams & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      const result = await this.appClient.send.call(AgriUsdCoinParamsFactory.burnTokens(params))
      return {...result, return: result.return as unknown as (undefined | AgriUsdCoinReturns['burn_tokens(uint64,account)byte[]'])}
    },

    /**
     * Makes a call to the AgriUSDCoin smart contract using the `transfer_tokens(uint64,account,account)byte[]` ABI method.
     *
     * @param params The params for the smart contract call
     * @returns The call result
     */
    transferTokens: async (params: CallParams<AgriUsdCoinArgs['obj']['transfer_tokens(uint64,account,account)byte[]'] | AgriUsdCoinArgs['tuple']['transfer_tokens(uint64,account,account)byte[]']> & SendParams & {onComplete?: OnApplicationComplete.NoOpOC}) => {
      const result = await this.appClient.send.call(AgriUsdCoinParamsFactory.transferTokens(params))
      return {...result, return: result.return as unknown as (undefined | AgriUsdCoinReturns['transfer_tokens(uint64,account,account)byte[]'])}
    },

  }

  /**
   * Clone this app client with different params
   *
   * @param params The params to use for the the cloned app client. Omit a param to keep the original value. Set a param to override the original value. Setting to undefined will clear the original value.
   * @returns A new app client with the altered params
   */
  public clone(params: CloneAppClientParams) {
    return new AgriUsdCoinClient(this.appClient.clone(params))
  }

  /**
   * Makes a readonly (simulated) call to the AgriUSDCoin smart contract using the `get_asset_id()uint64` ABI method.
   * 
   * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
   *
   * @param params The params for the smart contract call
   * @returns The call result
   */
  async getAssetId(params: CallParams<AgriUsdCoinArgs['obj']['get_asset_id()uint64'] | AgriUsdCoinArgs['tuple']['get_asset_id()uint64']> = {args: []}) {
    const result = await this.appClient.send.call(AgriUsdCoinParamsFactory.getAssetId(params))
    return result.return as unknown as AgriUsdCoinReturns['get_asset_id()uint64']
  }

  /**
   * Makes a readonly (simulated) call to the AgriUSDCoin smart contract using the `get_minted_tokens()uint64` ABI method.
   * 
   * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
   *
   * @param params The params for the smart contract call
   * @returns The call result
   */
  async getMintedTokens(params: CallParams<AgriUsdCoinArgs['obj']['get_minted_tokens()uint64'] | AgriUsdCoinArgs['tuple']['get_minted_tokens()uint64']> = {args: []}) {
    const result = await this.appClient.send.call(AgriUsdCoinParamsFactory.getMintedTokens(params))
    return result.return as unknown as AgriUsdCoinReturns['get_minted_tokens()uint64']
  }

  /**
   * Makes a readonly (simulated) call to the AgriUSDCoin smart contract using the `get_burnt_tokens()uint64` ABI method.
   * 
   * This method is a readonly method; calling it with onComplete of NoOp will result in a simulated transaction rather than a real transaction.
   *
   * @param params The params for the smart contract call
   * @returns The call result
   */
  async getBurntTokens(params: CallParams<AgriUsdCoinArgs['obj']['get_burnt_tokens()uint64'] | AgriUsdCoinArgs['tuple']['get_burnt_tokens()uint64']> = {args: []}) {
    const result = await this.appClient.send.call(AgriUsdCoinParamsFactory.getBurntTokens(params))
    return result.return as unknown as AgriUsdCoinReturns['get_burnt_tokens()uint64']
  }

  /**
   * Methods to access state for the current AgriUSDCoin app
   */
  state = {
    /**
     * Methods to access global state for the current AgriUSDCoin app
     */
    global: {
      /**
       * Get all current keyed values from global state
       */
      getAll: async (): Promise<Partial<Expand<GlobalKeysState>>> => {
        const result = await this.appClient.state.global.getAll()
        return {
          mintedTokens: result.minted_tokens,
          burntTokens: result.burnt_tokens,
          asset: result.asset,
        }
      },
      /**
       * Get the current value of the minted_tokens key in global state
       */
      mintedTokens: async (): Promise<bigint | undefined> => { return (await this.appClient.state.global.getValue("minted_tokens")) as bigint | undefined },
      /**
       * Get the current value of the burnt_tokens key in global state
       */
      burntTokens: async (): Promise<bigint | undefined> => { return (await this.appClient.state.global.getValue("burnt_tokens")) as bigint | undefined },
      /**
       * Get the current value of the asset key in global state
       */
      asset: async (): Promise<bigint | undefined> => { return (await this.appClient.state.global.getValue("asset")) as bigint | undefined },
    },
  }

  public newGroup(): AgriUsdCoinComposer {
    const client = this
    const composer = this.algorand.newGroup()
    let promiseChain:Promise<unknown> = Promise.resolve()
    const resultMappers: Array<undefined | ((x: ABIReturn | undefined) => any)> = []
    return {
      /**
       * Add a create()byte[] method call against the AgriUSDCoin contract
       */
      create(params: CallParams<AgriUsdCoinArgs['obj']['create()byte[]'] | AgriUsdCoinArgs['tuple']['create()byte[]']> & {onComplete?: OnApplicationComplete.NoOpOC}) {
        promiseChain = promiseChain.then(async () => composer.addAppCallMethodCall(await client.params.create(params)))
        resultMappers.push((v) => client.decodeReturnValue('create()byte[]', v))
        return this
      },
      /**
       * Add a get_asset_id()uint64 method call against the AgriUSDCoin contract
       */
      getAssetId(params: CallParams<AgriUsdCoinArgs['obj']['get_asset_id()uint64'] | AgriUsdCoinArgs['tuple']['get_asset_id()uint64']> & {onComplete?: OnApplicationComplete.NoOpOC}) {
        promiseChain = promiseChain.then(async () => composer.addAppCallMethodCall(await client.params.getAssetId(params)))
        resultMappers.push((v) => client.decodeReturnValue('get_asset_id()uint64', v))
        return this
      },
      /**
       * Add a get_minted_tokens()uint64 method call against the AgriUSDCoin contract
       */
      getMintedTokens(params: CallParams<AgriUsdCoinArgs['obj']['get_minted_tokens()uint64'] | AgriUsdCoinArgs['tuple']['get_minted_tokens()uint64']> & {onComplete?: OnApplicationComplete.NoOpOC}) {
        promiseChain = promiseChain.then(async () => composer.addAppCallMethodCall(await client.params.getMintedTokens(params)))
        resultMappers.push((v) => client.decodeReturnValue('get_minted_tokens()uint64', v))
        return this
      },
      /**
       * Add a get_burnt_tokens()uint64 method call against the AgriUSDCoin contract
       */
      getBurntTokens(params: CallParams<AgriUsdCoinArgs['obj']['get_burnt_tokens()uint64'] | AgriUsdCoinArgs['tuple']['get_burnt_tokens()uint64']> & {onComplete?: OnApplicationComplete.NoOpOC}) {
        promiseChain = promiseChain.then(async () => composer.addAppCallMethodCall(await client.params.getBurntTokens(params)))
        resultMappers.push((v) => client.decodeReturnValue('get_burnt_tokens()uint64', v))
        return this
      },
      /**
       * Add a mint_tokens(uint64,account)byte[] method call against the AgriUSDCoin contract
       */
      mintTokens(params: CallParams<AgriUsdCoinArgs['obj']['mint_tokens(uint64,account)byte[]'] | AgriUsdCoinArgs['tuple']['mint_tokens(uint64,account)byte[]']> & {onComplete?: OnApplicationComplete.NoOpOC}) {
        promiseChain = promiseChain.then(async () => composer.addAppCallMethodCall(await client.params.mintTokens(params)))
        resultMappers.push((v) => client.decodeReturnValue('mint_tokens(uint64,account)byte[]', v))
        return this
      },
      /**
       * Add a burn_tokens(uint64,account)byte[] method call against the AgriUSDCoin contract
       */
      burnTokens(params: CallParams<AgriUsdCoinArgs['obj']['burn_tokens(uint64,account)byte[]'] | AgriUsdCoinArgs['tuple']['burn_tokens(uint64,account)byte[]']> & {onComplete?: OnApplicationComplete.NoOpOC}) {
        promiseChain = promiseChain.then(async () => composer.addAppCallMethodCall(await client.params.burnTokens(params)))
        resultMappers.push((v) => client.decodeReturnValue('burn_tokens(uint64,account)byte[]', v))
        return this
      },
      /**
       * Add a transfer_tokens(uint64,account,account)byte[] method call against the AgriUSDCoin contract
       */
      transferTokens(params: CallParams<AgriUsdCoinArgs['obj']['transfer_tokens(uint64,account,account)byte[]'] | AgriUsdCoinArgs['tuple']['transfer_tokens(uint64,account,account)byte[]']> & {onComplete?: OnApplicationComplete.NoOpOC}) {
        promiseChain = promiseChain.then(async () => composer.addAppCallMethodCall(await client.params.transferTokens(params)))
        resultMappers.push((v) => client.decodeReturnValue('transfer_tokens(uint64,account,account)byte[]', v))
        return this
      },
      /**
       * Add a clear state call to the AgriUSDCoin contract
       */
      clearState(params: AppClientBareCallParams) {
        promiseChain = promiseChain.then(() => composer.addAppCall(client.params.clearState(params)))
        return this
      },
      addTransaction(txn: Transaction, signer?: TransactionSigner) {
        promiseChain = promiseChain.then(() => composer.addTransaction(txn, signer))
        return this
      },
      async composer() {
        await promiseChain
        return composer
      },
      async simulate(options?: SimulateOptions) {
        await promiseChain
        const result = await (!options ? composer.simulate() : composer.simulate(options))
        return {
          ...result,
          returns: result.returns?.map((val, i) => resultMappers[i] !== undefined ? resultMappers[i]!(val) : val.returnValue)
        }
      },
      async send(params?: SendParams) {
        await promiseChain
        const result = await composer.send(params)
        return {
          ...result,
          returns: result.returns?.map((val, i) => resultMappers[i] !== undefined ? resultMappers[i]!(val) : val.returnValue)
        }
      }
    } as unknown as AgriUsdCoinComposer
  }
}
export type AgriUsdCoinComposer<TReturns extends [...any[]] = []> = {
  /**
   * Calls the create()byte[] ABI method.
   *
   * Create the Agri USD Coin ASA.
   *
   * @param args The arguments for the contract call
   * @param params Any additional parameters for the call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  create(params?: CallParams<AgriUsdCoinArgs['obj']['create()byte[]'] | AgriUsdCoinArgs['tuple']['create()byte[]']>): AgriUsdCoinComposer<[...TReturns, AgriUsdCoinReturns['create()byte[]'] | undefined]>

  /**
   * Calls the get_asset_id()uint64 ABI method.
   *
   * @param args The arguments for the contract call
   * @param params Any additional parameters for the call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  getAssetId(params?: CallParams<AgriUsdCoinArgs['obj']['get_asset_id()uint64'] | AgriUsdCoinArgs['tuple']['get_asset_id()uint64']>): AgriUsdCoinComposer<[...TReturns, AgriUsdCoinReturns['get_asset_id()uint64'] | undefined]>

  /**
   * Calls the get_minted_tokens()uint64 ABI method.
   *
   * @param args The arguments for the contract call
   * @param params Any additional parameters for the call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  getMintedTokens(params?: CallParams<AgriUsdCoinArgs['obj']['get_minted_tokens()uint64'] | AgriUsdCoinArgs['tuple']['get_minted_tokens()uint64']>): AgriUsdCoinComposer<[...TReturns, AgriUsdCoinReturns['get_minted_tokens()uint64'] | undefined]>

  /**
   * Calls the get_burnt_tokens()uint64 ABI method.
   *
   * @param args The arguments for the contract call
   * @param params Any additional parameters for the call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  getBurntTokens(params?: CallParams<AgriUsdCoinArgs['obj']['get_burnt_tokens()uint64'] | AgriUsdCoinArgs['tuple']['get_burnt_tokens()uint64']>): AgriUsdCoinComposer<[...TReturns, AgriUsdCoinReturns['get_burnt_tokens()uint64'] | undefined]>

  /**
   * Calls the mint_tokens(uint64,account)byte[] ABI method.
   *
   * @param args The arguments for the contract call
   * @param params Any additional parameters for the call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  mintTokens(params?: CallParams<AgriUsdCoinArgs['obj']['mint_tokens(uint64,account)byte[]'] | AgriUsdCoinArgs['tuple']['mint_tokens(uint64,account)byte[]']>): AgriUsdCoinComposer<[...TReturns, AgriUsdCoinReturns['mint_tokens(uint64,account)byte[]'] | undefined]>

  /**
   * Calls the burn_tokens(uint64,account)byte[] ABI method.
   *
   * @param args The arguments for the contract call
   * @param params Any additional parameters for the call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  burnTokens(params?: CallParams<AgriUsdCoinArgs['obj']['burn_tokens(uint64,account)byte[]'] | AgriUsdCoinArgs['tuple']['burn_tokens(uint64,account)byte[]']>): AgriUsdCoinComposer<[...TReturns, AgriUsdCoinReturns['burn_tokens(uint64,account)byte[]'] | undefined]>

  /**
   * Calls the transfer_tokens(uint64,account,account)byte[] ABI method.
   *
   * @param args The arguments for the contract call
   * @param params Any additional parameters for the call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  transferTokens(params?: CallParams<AgriUsdCoinArgs['obj']['transfer_tokens(uint64,account,account)byte[]'] | AgriUsdCoinArgs['tuple']['transfer_tokens(uint64,account,account)byte[]']>): AgriUsdCoinComposer<[...TReturns, AgriUsdCoinReturns['transfer_tokens(uint64,account,account)byte[]'] | undefined]>

  /**
   * Makes a clear_state call to an existing instance of the AgriUSDCoin smart contract.
   *
   * @param args The arguments for the bare call
   * @returns The typed transaction composer so you can fluently chain multiple calls or call execute to execute all queued up transactions
   */
  clearState(params?: AppClientBareCallParams): AgriUsdCoinComposer<[...TReturns, undefined]>

  /**
   * Adds a transaction to the composer
   *
   * @param txn A transaction to add to the transaction group
   * @param signer The optional signer to use when signing this transaction.
   */
  addTransaction(txn: Transaction, signer?: TransactionSigner): AgriUsdCoinComposer<TReturns>
  /**
   * Returns the underlying AtomicTransactionComposer instance
   */
  composer(): Promise<TransactionComposer>
  /**
   * Simulates the transaction group and returns the result
   */
  simulate(): Promise<AgriUsdCoinComposerResults<TReturns> & { simulateResponse: SimulateResponse }>
  simulate(options: SkipSignaturesSimulateOptions): Promise<AgriUsdCoinComposerResults<TReturns> & { simulateResponse: SimulateResponse }>
  simulate(options: RawSimulateOptions): Promise<AgriUsdCoinComposerResults<TReturns> & { simulateResponse: SimulateResponse }>
  /**
   * Sends the transaction group to the network and returns the results
   */
  send(params?: SendParams): Promise<AgriUsdCoinComposerResults<TReturns>>
}
export type AgriUsdCoinComposerResults<TReturns extends [...any[]]> = Expand<SendAtomicTransactionComposerResults & {
  returns: TReturns
}>

