from django.urls import path, include
from oauth2_provider import urls as oauth2_urls
from . import views

app_name = 'oauth2_auth'

urlpatterns = [
    # OAuth2 Provider URLs (includes /authorize/, /token/, /revoke_token/, etc.)
    path('', include(oauth2_urls)),

    # Custom OAuth2 endpoints
    path('introspect/', views.token_introspect, name='introspect'),
    path('jwks/', views.jwks, name='jwks'),

    # Authentication flow endpoints
    path('device/register/', views.register_device, name='register_device'),

    # OAuth2 Client Management
    path('clients/', views.list_oauth2_clients, name='list_oauth2_clients'),
    path('clients/create/', views.create_oauth2_client, name='create_oauth2_client'),
    path('clients/rotate-secret/', views.rotate_client_secret, name='rotate_client_secret'),
    path('clients/revoke/', views.revoke_oauth2_client, name='revoke_oauth2_client'),

    # Token Management
    path('tokens/revoke/', views.revoke_user_tokens, name='revoke_user_tokens'),
    path('tokens/stats/', views.token_statistics, name='token_statistics'),

    # Security Logging
    path('security/events/', views.user_security_events, name='user_security_events'),
    path('security/summary/', views.security_summary, name='security_summary'),

    # OpenID Connect Discovery
    path('.well-known/openid_configuration/', views.well_known_openid_configuration, name='openid_configuration'),
]
