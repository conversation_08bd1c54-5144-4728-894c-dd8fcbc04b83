import { useAuthStore } from '@/stores/authStore'
import { VITE_BACKEND_URL } from '@/utils/variable'
import axios, { AxiosRequestConfig, CanceledError } from 'axios'
import DeviceService from './deviceService'
import SecurityService from './securityService'

const backendURL = VITE_BACKEND_URL

if (!backendURL) {
  throw new Error('VITE_BACKEND_URL is not defined in environment variables.')
}

const apiClient = axios.create({
  baseURL: backendURL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token, device info, and handle token refresh
apiClient.interceptors.request.use(
  async (config) => {
    const authStore = useAuthStore.getState()

    // Check if token is expired and refresh if needed
    if (authStore.tokenData && authStore.isTokenExpired() && !authStore.isRefreshTokenExpired()) {
      await authStore.refreshToken()
    }

    const token = useAuthStore.getState().token
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // Add device information and security headers
    try {
      const deviceInfo = DeviceService.getDeviceInfo()

      // Generate secure headers with request signing
      const secureHeaders = await SecurityService.generateSecureHeaders(
        config.method?.toUpperCase() || 'GET',
        config.url || '',
        config.data,
        deviceInfo.deviceId,
        deviceInfo.fingerprint
      )

      // Add all secure headers
      Object.assign(config.headers, secureHeaders)

      // Add additional device security information
      config.headers['X-Device-Security-Score'] = DeviceService.getDeviceSecurityScore().toString()

      // Detect and report security threats
      const securityCheck = SecurityService.detectSecurityThreats()
      if (securityCheck.threats.length > 0) {
        config.headers['X-Security-Threats'] = JSON.stringify(securityCheck.threats)
        config.headers['X-Risk-Level'] = securityCheck.riskLevel
      }

    } catch (error) {
      console.warn('Failed to add security information to request:', error)

      // Fallback to basic device info
      try {
        const deviceInfo = DeviceService.getDeviceInfo()
        config.headers['X-Device-ID'] = deviceInfo.deviceId
        config.headers['X-Device-Fingerprint'] = deviceInfo.fingerprint
      } catch (fallbackError) {
        console.error('Failed to add even basic device info:', fallbackError)
      }
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// Response interceptor to handle token expiration, automatic refresh, and security validation
apiClient.interceptors.response.use(
  (response) => {
    // Validate response integrity
    try {
      if (response.data && !SecurityService.validateResponseIntegrity(response.data)) {
        console.warn('Response integrity validation failed')
      }
    } catch (error) {
      console.warn('Response validation error:', error)
    }

    return response
  },
  async (error) => {
    const originalRequest = error.config

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      const authStore = useAuthStore.getState()

      // Try to refresh token
      if (authStore.tokenData && !authStore.isRefreshTokenExpired()) {
        const refreshed = await authStore.refreshToken()

        if (refreshed) {
          // Retry the original request with new token
          const newToken = useAuthStore.getState().token
          originalRequest.headers.Authorization = `Bearer ${newToken}`
          return apiClient(originalRequest)
        }
      }

      // If refresh failed or refresh token is expired, logout
      authStore.logout()
      window.location.href = '/login'
    }

    return Promise.reject(error)
  },
)

export default apiClient
export type { AxiosRequestConfig, CanceledError }
