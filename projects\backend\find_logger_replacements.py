#!/usr/bin/env python3
"""
Script to find all logger_utils function calls that should be replaced with direct logger calls.
"""

import os
import re
from pathlib import Path

def find_logger_function_calls(directory):
    """Find all logger_utils function calls in Python files."""
    
    # Functions to replace
    target_functions = [
        'log_operation_info',
        'log_request_info', 
        'log_response_info',
        'log_error_with_traceback',
        'log_api_call',
        'log_database_operation',
        'log_performance_metric'
    ]
    
    results = {}
    
    for root, dirs, files in os.walk(directory):
        # Skip certain directories
        if any(skip in root for skip in ['.git', '__pycache__', '.venv', 'node_modules']):
            continue
            
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, directory)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    file_results = []
                    lines = content.split('\n')
                    
                    for line_num, line in enumerate(lines, 1):
                        for func in target_functions:
                            if func in line and not line.strip().startswith('#'):
                                file_results.append({
                                    'line_num': line_num,
                                    'function': func,
                                    'line': line.strip()
                                })
                    
                    if file_results:
                        results[relative_path] = file_results
                        
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
    
    return results

def print_replacement_suggestions(results):
    """Print suggestions for replacing logger function calls."""
    
    print("🔍 LOGGER FUNCTION REPLACEMENT ANALYSIS")
    print("=" * 60)
    
    total_replacements = 0
    
    for file_path, calls in results.items():
        print(f"\n📁 {file_path}")
        print("-" * 40)
        
        for call in calls:
            total_replacements += 1
            print(f"  Line {call['line_num']}: {call['function']}")
            print(f"    {call['line']}")
            
            # Suggest replacement
            if call['function'] == 'log_operation_info':
                print(f"    💡 Replace with: logger.info(...)")
            elif call['function'] == 'log_request_info':
                print(f"    💡 Replace with: logger.info(f'Request: {{request.method}} {{request.path}}', extra={{...}})")
            elif call['function'] == 'log_response_info':
                print(f"    💡 Replace with: logger.info(f'Response: {{status_code}}', extra={{...}})")
            elif call['function'] == 'log_error_with_traceback':
                print(f"    💡 Replace with: logger.error(..., exc_info=True)")
            else:
                print(f"    💡 Replace with: logger.info/warning/error(...)")
            print()
    
    print(f"\n📊 SUMMARY:")
    print(f"   Files with logger function calls: {len(results)}")
    print(f"   Total function calls to replace: {total_replacements}")
    
    print(f"\n🎯 PRIORITY FILES TO UPDATE:")
    priority_files = sorted(results.items(), key=lambda x: len(x[1]), reverse=True)
    for file_path, calls in priority_files[:5]:
        print(f"   {file_path}: {len(calls)} calls")

if __name__ == "__main__":
    # Run from backend directory
    backend_dir = "."
    results = find_logger_function_calls(backend_dir)
    print_replacement_suggestions(results)
