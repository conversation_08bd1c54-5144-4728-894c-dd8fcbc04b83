from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
import uuid
from .config import oauth2_security_config

User = get_user_model()


class DeviceToken(models.Model):
    """
    Model for device-specific tokens and trust management
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="device_tokens"
    )
    device_id = models.CharField(max_length=255)
    device_name = models.CharField(max_length=255)
    device_type = models.CharField(
        max_length=50,
        choices=[
            ("web", "Web Browser"),
            ("mobile", "Mobile App"),
            ("desktop", "Desktop App"),
            ("api", "API Client"),
        ],
    )

    # Trust and security
    is_trusted = models.BooleanField(default=False)
    trust_expires = models.DateTimeField(null=True, blank=True)
    fingerprint = models.CharField(max_length=255, blank=True, null=True)

    # Tracking
    first_seen = models.DateTimeField(auto_now_add=True)
    last_seen = models.DateTimeField(auto_now=True)
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)

    # Security flags
    is_blocked = models.BooleanField(default=False)
    blocked_at = models.DateTimeField(null=True, blank=True)
    blocked_reason = models.TextField(blank=True, null=True)

    class Meta:
        verbose_name = "Device Token"
        verbose_name_plural = "Device Tokens"
        unique_together = ["user", "device_id"]
        indexes = [
            models.Index(fields=["user", "device_id"]),
            models.Index(fields=["device_id"]),
            models.Index(fields=["is_trusted"]),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.device_name}"

    def is_trust_valid(self):
        """Check if device trust is still valid"""
        if not self.is_trusted:
            return False
        if self.trust_expires and self.trust_expires < timezone.now():
            return False
        return True


class SecurityEvent(models.Model):
    """
    Model for tracking security events and audit logs
    """

    EVENT_TYPES = [
        # Authentication Events
        ("login", "Login"),
        ("logout", "Logout"),
        ("failed_login", "Failed Login"),
        ("account_locked", "Account Locked"),
        ("account_unlocked", "Account Unlocked"),
        ("account_permanently_locked", "Account Permanently Locked"),
        ("locked_account_login_attempt", "Locked Account Login Attempt"),
        (
            "password_reset_attempt_locked_account",
            "Password Reset Attempt on Locked Account",
        ),
        ("inactive_user_login_attempt", "Inactive User Login Attempt"),
        # Token Events
        ("token_issued", "Token Issued"),
        ("token_refreshed", "Token Refreshed"),
        ("token_revoked", "Token Revoked"),
        ("token_expired", "Token Expired"),
        ("invalid_token_usage", "Invalid Token Usage"),
        ("token_ip_change", "Token IP Change"),
        ("activation_token_generated", "Activation Token Generated"),
        ("activation_token_used", "Activation Token Used"),
        ("password_reset_token_generated", "Password Reset Token Generated"),
        ("password_reset_token_used", "Password Reset Token Used"),
        ("token_invalidated", "Token Invalidated"),
        # Device Events
        ("device_registered", "Device Registered"),
        ("device_verified", "Device Verified"),
        ("device_blocked", "Device Blocked"),
        ("device_unblocked", "Device Unblocked"),
        ("new_device_detected", "New Device Detected"),
        ("device_fingerprint_changed", "Device Fingerprint Changed"),
        # Security Events
        ("suspicious_activity", "Suspicious Activity"),
        ("brute_force_attempt", "Brute Force Attempt"),
        ("rate_limit_exceeded", "Rate Limit Exceeded"),
        ("ip_blocked", "IP Blocked"),
        ("security_alert_sent", "Security Alert Sent"),
        # Account Events
        ("user_registered", "User Registered"),
        ("account_activated", "Account Activated"),
        ("password_changed", "Password Changed"),
        ("password_reset_requested", "Password Reset Requested"),
        ("password_reset_completed", "Password Reset Completed"),
        ("email_changed", "Email Changed"),
        ("profile_updated", "Profile Updated"),
        # OAuth2 Events
        ("oauth2_client_created", "OAuth2 Client Created"),
        ("oauth2_client_revoked", "OAuth2 Client Revoked"),
        ("client_secret_rotated", "Client Secret Rotated"),
        ("authorization_granted", "Authorization Granted"),
        ("authorization_denied", "Authorization Denied"),
        # Administrative Events
        ("admin_login", "Admin Login"),
        ("admin_action", "Admin Action"),
        ("user_impersonation", "User Impersonation"),
        ("bulk_operation", "Bulk Operation"),
        ("system_maintenance", "System Maintenance"),
        # API Events
        ("api_access", "API Access"),
        ("api_rate_limit", "API Rate Limit"),
        ("api_error", "API Error"),
        ("endpoint_access", "Endpoint Access"),
        ("access_denied", "Access Denied"),
        # Data Events
        ("data_export", "Data Export"),
        ("data_import", "Data Import"),
        ("data_deletion", "Data Deletion"),
        ("sensitive_data_access", "Sensitive Data Access"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="security_events",
        null=True,
        blank=True,
    )
    event_type = models.CharField(max_length=50, choices=EVENT_TYPES)
    description = models.TextField()

    # Context information
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)
    device_id = models.CharField(max_length=255, blank=True, null=True)

    # Additional data
    metadata = models.JSONField(default=dict, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Security Event"
        verbose_name_plural = "Security Events"
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["user", "event_type"]),
            models.Index(fields=["created_at"]),
            models.Index(fields=["event_type"]),
        ]

    def __str__(self):
        user_info = f"{self.user.email}" if self.user else "Anonymous"
        return f"{user_info} - {self.get_event_type_display()} at {self.created_at}"


class SecureToken(models.Model):
    """
    Fintech-grade secure token model for account activation and password reset
    Provides unique tokens with proper expiration and invalidation
    """

    TOKEN_TYPES = [
        ("activation", "Account Activation"),
        ("password_reset", "Password Reset"),
        ("email_verification", "Email Verification"),
        ("phone_verification", "Phone Verification"),
    ]

    TOKEN_STATUS = [
        ("active", "Active"),
        ("used", "Used"),
        ("expired", "Expired"),
        ("invalidated", "Invalidated"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="secure_tokens"
    )
    token_type = models.CharField(max_length=20, choices=TOKEN_TYPES)
    token_hash = models.CharField(max_length=128, unique=True, db_index=True)
    status = models.CharField(max_length=20, choices=TOKEN_STATUS, default="active")

    # Security metadata
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    device_fingerprint = models.CharField(max_length=64, null=True, blank=True)

    # Timing controls
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    used_at = models.DateTimeField(null=True, blank=True)
    invalidated_at = models.DateTimeField(null=True, blank=True)

    # Usage tracking
    attempt_count = models.PositiveIntegerField(default=0)
    max_attempts = models.PositiveIntegerField(
        default=oauth2_security_config.DEFAULT_MAX_ATTEMPTS
    )

    # Additional metadata
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        verbose_name = "Secure Token"
        verbose_name_plural = "Secure Tokens"
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["user", "token_type", "status"]),
            models.Index(fields=["expires_at"]),
            models.Index(fields=["created_at"]),
            models.Index(fields=["token_hash"]),
            models.Index(fields=["status"]),
        ]

    def save(self, *args, **kwargs):
        # Set expiration based on token type if not already set using centralized config
        if not self.expires_at:
            token_expiration = oauth2_security_config.TOKEN_EXPIRATION.get(
                self.token_type
            )
            if token_expiration:
                self.expires_at = timezone.now() + timedelta(hours=token_expiration)
            else:
                # Fallback for unknown token types
                self.expires_at = timezone.now() + timedelta(hours=2)

        super().save(*args, **kwargs)

    def is_valid(self):
        """Check if token is valid for use"""
        if self.status != "active":
            return False
        if timezone.now() > self.expires_at:
            self.status = "expired"
            self.save(update_fields=["status"])
            return False
        if self.attempt_count >= self.max_attempts:
            self.status = "invalidated"
            self.invalidated_at = timezone.now()
            self.save(update_fields=["status", "invalidated_at"])
            return False
        return True

    def mark_used(self):
        """Mark token as used"""
        self.status = "used"
        self.used_at = timezone.now()
        self.save(update_fields=["status", "used_at"])

    def invalidate(self, reason=None):
        """Invalidate token"""
        self.status = "invalidated"
        self.invalidated_at = timezone.now()
        if reason:
            self.metadata["invalidation_reason"] = reason
        self.save(update_fields=["status", "invalidated_at", "metadata"])

    def increment_attempt(self):
        """Increment attempt counter"""
        self.attempt_count += 1
        self.save(update_fields=["attempt_count"])

    # Token generation and hashing methods moved to utils.py for centralized management
    # Use generate_secure_token() and hash_token() from oauth2_auth.utils instead

    def __str__(self):
        return f"{self.user.email} - {self.token_type} - {self.status}"
