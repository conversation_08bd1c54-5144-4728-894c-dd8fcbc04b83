"""
Correlation ID middleware for Django applications.
Provides request correlation ID functionality similar to django-correlation-id package.
"""

import logging
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
from .logger_utils import (
    generate_correlation_id,
    set_request_context,
    clear_request_context,
    get_current_unique_id,
)

logger = logging.getLogger(__name__)


class CorrelationIdMiddleware(MiddlewareMixin):
    """
    Middleware that adds correlation IDs to requests for tracking across services.
    
    Features:
    - Generates unique correlation ID for each request
    - Reads correlation ID from incoming headers (X-Correlation-ID, X-Request-ID)
    - Adds correlation ID to response headers
    - Integrates with existing logging infrastructure
    - Thread-safe using thread-local storage
    """
    
    # Header names to check for incoming correlation IDs
    CORRELATION_ID_HEADERS = [
        'HTTP_X_CORRELATION_ID',
        'HTTP_X_REQUEST_ID', 
        'HTTP_X_TRACE_ID',
        'HTTP_REQUEST_ID',
    ]
    
    # Response header name
    RESPONSE_HEADER_NAME = getattr(settings, 'CORRELATION_ID_RESPONSE_HEADER', 'X-Correlation-ID')
    
    # Whether to generate new ID if none provided
    GENERATE_IF_NOT_EXISTS = getattr(settings, 'CORRELATION_ID_GENERATE_IF_NOT_EXISTS', True)
    
    # Paths to exclude from correlation ID processing
    EXCLUDED_PATHS = getattr(settings, 'CORRELATION_ID_EXCLUDED_PATHS', [
        '/static/',
        '/media/',
        '/health/',
        '/favicon.ico',
        '/admin/jsi18n/',
    ])

    def process_request(self, request):
        """
        Process incoming request and set up correlation ID.
        """
        # Skip processing for excluded paths
        if any(request.path.startswith(path) for path in self.EXCLUDED_PATHS):
            return None
            
        correlation_id = None
        
        # Try to get correlation ID from headers
        for header_name in self.CORRELATION_ID_HEADERS:
            correlation_id = request.META.get(header_name)
            if correlation_id:
                logger.debug(f"Found correlation ID in header {header_name}: {correlation_id}")
                break
        
        # Generate new correlation ID if none found and generation is enabled
        if not correlation_id and self.GENERATE_IF_NOT_EXISTS:
            correlation_id = generate_correlation_id()
            logger.debug(f"Generated new correlation ID: {correlation_id}")
        
        # Set correlation ID in request and thread-local storage
        if correlation_id:
            request.correlation_id = correlation_id
            
            # Set request context for logging integration
            set_request_context(
                unique_id=correlation_id,
                request_path=request.path,
                request_method=request.method,
                correlation_id=correlation_id,
            )
            
            logger.debug(f"Set correlation ID for request {request.method} {request.path}: {correlation_id}")
        
        return None

    def process_response(self, request, response):
        """
        Process response and add correlation ID header.
        """
        # Skip processing for excluded paths
        if any(request.path.startswith(path) for path in self.EXCLUDED_PATHS):
            return response
            
        # Get correlation ID from request or thread-local storage
        correlation_id = getattr(request, 'correlation_id', None)
        if not correlation_id:
            correlation_id = get_current_unique_id()
        
        # Add correlation ID to response headers
        if correlation_id:
            response[self.RESPONSE_HEADER_NAME] = correlation_id
            logger.debug(f"Added correlation ID to response header: {correlation_id}")
        
        return response

    def process_exception(self, request, exception):
        """
        Process exceptions and ensure correlation ID is available for error logging.
        """
        correlation_id = getattr(request, 'correlation_id', None)
        if not correlation_id:
            correlation_id = get_current_unique_id()
            
        if correlation_id:
            logger.error(
                f"Exception in request with correlation ID {correlation_id}: {exception}",
                exc_info=True,
                extra={'correlation_id': correlation_id}
            )
        
        return None


class CorrelationIdLoggingFilter(logging.Filter):
    """
    Logging filter that adds correlation ID to log records.
    """
    
    def filter(self, record):
        """
        Add correlation ID to log record if available.
        """
        correlation_id = get_current_unique_id()
        if correlation_id:
            record.correlation_id = correlation_id
        else:
            record.correlation_id = 'N/A'
        return True


def get_correlation_id():
    """
    Get the current correlation ID for the request.
    
    Returns:
        str: Current correlation ID or None if not set
    """
    return get_current_unique_id()


def set_correlation_id(correlation_id):
    """
    Set the correlation ID for the current request.
    
    Args:
        correlation_id (str): The correlation ID to set
    """
    set_request_context(unique_id=correlation_id, correlation_id=correlation_id)
