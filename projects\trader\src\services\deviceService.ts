/**
 * Device Identification and Security Service
 * Provides secure device identification, fingerprinting, and storage
 */

interface DeviceInfo {
  deviceId: string
  deviceName: string
  fingerprint: string
  timestamp: number
  userAgent: string
  platform: string
  language: string
  timezone: string
  screenResolution: string
}

interface StoredDeviceData {
  deviceId: string
  deviceName: string
  fingerprint: string
  createdAt: number
  lastUsed: number
}

class DeviceService {
  private static readonly DEVICE_STORAGE_KEY = 'agritram_device_info'
  private static readonly DEVICE_FINGERPRINT_KEY = 'agritram_device_fingerprint'
  private static readonly DEVICE_ROTATION_DAYS = 30 // Rotate device ID every 30 days

  /**
   * Generate a cryptographically secure device ID
   */
  private static generateSecureDeviceId(): string {
    // Use crypto.getRandomValues for secure random generation
    const array = new Uint8Array(32)
    crypto.getRandomValues(array)
    
    // Convert to base64url (URL-safe base64)
    const base64 = btoa(String.fromCharCode(...array))
    return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '')
  }

  /**
   * Generate device fingerprint based on browser characteristics
   */
  private static generateDeviceFingerprint(): string {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    // Canvas fingerprinting
    if (ctx) {
      ctx.textBaseline = 'top'
      ctx.font = '14px Arial'
      ctx.fillText('Device fingerprint test', 2, 2)
    }
    const canvasFingerprint = canvas.toDataURL()

    // Collect browser characteristics
    const characteristics = [
      navigator.userAgent,
      navigator.language,
      navigator.languages?.join(',') || '',
      navigator.platform,
      navigator.hardwareConcurrency?.toString() || '',
      screen.width + 'x' + screen.height,
      screen.colorDepth?.toString() || '',
      new Date().getTimezoneOffset().toString(),
      navigator.cookieEnabled.toString(),
      navigator.doNotTrack || '',
      canvasFingerprint,
      // WebGL fingerprinting
      this.getWebGLFingerprint(),
      // Audio context fingerprinting
      this.getAudioFingerprint()
    ].join('|')

    // Hash the characteristics
    return this.hashString(characteristics)
  }

  /**
   * Get WebGL fingerprint
   */
  private static getWebGLFingerprint(): string {
    try {
      const canvas = document.createElement('canvas')
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
      
      if (!gl) return ''
      
      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info')
      if (!debugInfo) return ''
      
      const vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL)
      const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)
      
      return `${vendor}|${renderer}`
    } catch (e) {
      return ''
    }
  }

  /**
   * Get audio context fingerprint
   */
  private static getAudioFingerprint(): string {
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      const oscillator = audioContext.createOscillator()
      const analyser = audioContext.createAnalyser()
      const gainNode = audioContext.createGain()
      
      oscillator.type = 'triangle'
      oscillator.frequency.setValueAtTime(10000, audioContext.currentTime)
      
      gainNode.gain.setValueAtTime(0, audioContext.currentTime)
      oscillator.connect(analyser)
      analyser.connect(gainNode)
      gainNode.connect(audioContext.destination)
      
      oscillator.start(0)
      
      const frequencyData = new Uint8Array(analyser.frequencyBinCount)
      analyser.getByteFrequencyData(frequencyData)
      
      oscillator.stop()
      audioContext.close()
      
      return Array.from(frequencyData).slice(0, 30).join(',')
    } catch (e) {
      return ''
    }
  }

  /**
   * Simple hash function for fingerprinting
   */
  private static hashString(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36)
  }

  /**
   * Get device name based on user agent and platform
   */
  private static getDeviceName(): string {
    const ua = navigator.userAgent
    const platform = navigator.platform
    
    // Mobile devices
    if (/iPhone/.test(ua)) return 'iPhone'
    if (/iPad/.test(ua)) return 'iPad'
    if (/Android/.test(ua)) return 'Android Device'
    
    // Desktop platforms
    if (/Mac/.test(platform)) return 'Mac'
    if (/Win/.test(platform)) return 'Windows PC'
    if (/Linux/.test(platform)) return 'Linux PC'
    
    // Browsers
    if (/Chrome/.test(ua)) return 'Chrome Browser'
    if (/Firefox/.test(ua)) return 'Firefox Browser'
    if (/Safari/.test(ua)) return 'Safari Browser'
    if (/Edge/.test(ua)) return 'Edge Browser'
    
    return 'Unknown Device'
  }

  /**
   * Check if device ID needs rotation
   */
  private static shouldRotateDevice(createdAt: number): boolean {
    const now = Date.now()
    const daysSinceCreation = (now - createdAt) / (1000 * 60 * 60 * 24)
    return daysSinceCreation > this.DEVICE_ROTATION_DAYS
  }

  /**
   * Get or create device information
   */
  static getDeviceInfo(): DeviceInfo {
    try {
      // Check for existing device data
      const storedData = localStorage.getItem(this.DEVICE_STORAGE_KEY)
      const storedFingerprint = localStorage.getItem(this.DEVICE_FINGERPRINT_KEY)
      
      let deviceData: StoredDeviceData | null = null
      
      if (storedData) {
        try {
          deviceData = JSON.parse(storedData)
        } catch (e) {
          console.warn('Invalid stored device data, generating new device ID')
        }
      }

      // Generate current fingerprint
      const currentFingerprint = this.generateDeviceFingerprint()
      
      // Check if we need to generate new device ID
      const needsNewDevice = !deviceData || 
                            this.shouldRotateDevice(deviceData.createdAt) ||
                            (storedFingerprint && storedFingerprint !== currentFingerprint)

      if (needsNewDevice) {
        // Generate new device data
        const newDeviceId = this.generateSecureDeviceId()
        const now = Date.now()
        
        deviceData = {
          deviceId: newDeviceId,
          deviceName: this.getDeviceName(),
          fingerprint: currentFingerprint,
          createdAt: now,
          lastUsed: now
        }
        
        // Store securely
        localStorage.setItem(this.DEVICE_STORAGE_KEY, JSON.stringify(deviceData))
        localStorage.setItem(this.DEVICE_FINGERPRINT_KEY, currentFingerprint)
      } else if (deviceData) {
        // Update last used timestamp
        deviceData.lastUsed = Date.now()
        localStorage.setItem(this.DEVICE_STORAGE_KEY, JSON.stringify(deviceData))
      }

      // Return complete device info
      return {
        deviceId: deviceData!.deviceId,
        deviceName: deviceData!.deviceName,
        fingerprint: currentFingerprint,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        screenResolution: `${screen.width}x${screen.height}`
      }
    } catch (error) {
      console.error('Error getting device info:', error)
      
      // Fallback: generate minimal device info
      const fallbackDeviceId = this.generateSecureDeviceId()
      return {
        deviceId: fallbackDeviceId,
        deviceName: this.getDeviceName(),
        fingerprint: this.generateDeviceFingerprint(),
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        screenResolution: `${screen.width}x${screen.height}`
      }
    }
  }

  /**
   * Clear device information (for logout or security reset)
   */
  static clearDeviceInfo(): void {
    localStorage.removeItem(this.DEVICE_STORAGE_KEY)
    localStorage.removeItem(this.DEVICE_FINGERPRINT_KEY)
  }

  /**
   * Validate device fingerprint consistency
   */
  static validateDeviceFingerprint(): boolean {
    try {
      const storedFingerprint = localStorage.getItem(this.DEVICE_FINGERPRINT_KEY)
      const currentFingerprint = this.generateDeviceFingerprint()
      
      return storedFingerprint === currentFingerprint
    } catch (error) {
      console.error('Error validating device fingerprint:', error)
      return false
    }
  }

  /**
   * Get device security score (0-100)
   */
  static getDeviceSecurityScore(): number {
    let score = 0
    
    // Check for secure context (HTTPS)
    if (location.protocol === 'https:') score += 20
    
    // Check for modern browser features
    if (window.crypto && window.crypto.getRandomValues) score += 20
    if (window.localStorage) score += 10
    if (window.sessionStorage) score += 10
    
    // Check for fingerprinting capabilities
    if (document.createElement('canvas').getContext) score += 15
    if (window.AudioContext || (window as any).webkitAudioContext) score += 15
    
    // Check for security headers (basic check)
    if (document.referrerPolicy) score += 10
    
    return Math.min(score, 100)
  }
}

export default DeviceService
export type { DeviceInfo, StoredDeviceData }
