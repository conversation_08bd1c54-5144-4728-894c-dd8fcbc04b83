/**
 * OTP (One-Time Password) Service
 * Handles device verification OTP flow
 */

import apiClient from './apiClient'

interface OTPVerificationData {
  email: string
  password: string
  device_id: string
  device_name: string
  otp_code: string
}

interface OTPResponse {
  success: boolean
  message: string
  requires_otp?: boolean
  device_id?: string
}

interface LoginWithOTPResponse {
  token: string
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  refresh_expires_in: number
  message: string
  user: any
}

class OTPService {
  /**
   * Initiate login and check if O<PERSON> is required
   */
  static async initiateLogin(email: string, password: string, deviceId: string, deviceName: string): Promise<OTPResponse> {
    try {
      const response = await apiClient.post('/user/login/', {
        email,
        password,
        device_id: deviceId,
        device_name: deviceName
      })

      // If login is successful without OTP
      return {
        success: true,
        message: response.data.success?.message || 'Login successful',
        requires_otp: false
      }
    } catch (error: any) {
      // Check if error is due to device verification requirement
      if (error.response?.status === 403 && error.response?.data?.error === 'device_verification_required') {
        return {
          success: false,
          message: error.response.data.message || 'Device verification required',
          requires_otp: true,
          device_id: error.response.data.device_id || deviceId
        }
      }
      
      // Re-throw other errors
      throw error
    }
  }

  /**
   * Verify OTP and complete login
   */
  static async verifyOTPAndLogin(data: OTPVerificationData): Promise<LoginWithOTPResponse> {
    try {
      const response = await apiClient.post('/user/login/', {
        email: data.email,
        password: data.password,
        device_id: data.device_id,
        device_name: data.device_name,
        otp_code: data.otp_code
      })

      const responseData = response.data.data || response.data

      if (responseData && responseData.access_token && responseData.user) {
        return {
          token: responseData.access_token,
          access_token: responseData.access_token,
          refresh_token: responseData.refresh_token,
          token_type: responseData.token_type || 'Bearer',
          expires_in: responseData.expires_in,
          refresh_expires_in: responseData.refresh_expires_in,
          message: response.data.success?.message || 'Login successful',
          user: responseData.user,
        }
      } else {
        throw new Error('Login response data is incomplete or missing required fields.')
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message)
      }
      throw error
    }
  }

  /**
   * Resend OTP for device verification
   */
  static async resendOTP(email: string, deviceId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await apiClient.post('/user/resend-device-otp/', {
        email,
        device_id: deviceId
      })

      return {
        success: true,
        message: response.data.message || 'OTP resent successfully'
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to resend OTP')
    }
  }

  /**
   * Validate OTP format (6 digits)
   */
  static validateOTPFormat(otp: string): boolean {
    return /^\d{6}$/.test(otp)
  }

  /**
   * Format OTP input (remove non-digits, limit to 6 characters)
   */
  static formatOTPInput(input: string): string {
    return input.replace(/\D/g, '').slice(0, 6)
  }

  /**
   * Check if OTP is complete (6 digits)
   */
  static isOTPComplete(otp: string): boolean {
    return otp.length === 6 && this.validateOTPFormat(otp)
  }
}

export default OTPService
export type { OTPVerificationData, OTPResponse, LoginWithOTPResponse }
