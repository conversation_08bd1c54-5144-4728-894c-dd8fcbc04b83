"""
Custom exception classes for the Agritram API.

These exceptions work with the StandardErrorResponse utility to provide
consistent error formatting across the application.
"""

from rest_framework import status
from typing import Optional, Dict, Any


class AgritramAPIException(Exception):
    """
    Base exception class for all Agritram API exceptions.
    
    All custom exceptions should inherit from this class to ensure
    consistent error handling and formatting.
    """
    
    def __init__(
        self,
        message: str,
        code: str = "API_ERROR",
        details: Optional[str] = None,
        actions: Optional[str] = None,
        status_code: int = status.HTTP_400_BAD_REQUEST,
        extra_data: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.code = code
        self.details = details
        self.actions = actions
        self.status_code = status_code
        self.extra_data = extra_data or {}
        super().__init__(message)


class ValidationException(AgritramAPIException):
    """Exception for validation errors."""
    
    def __init__(
        self,
        message: str = "Validation failed",
        details: Optional[str] = None,
        field_errors: Optional[Dict[str, Any]] = None
    ):
        extra_data = {}
        if field_errors:
            extra_data["field_errors"] = field_errors
            
        super().__init__(
            message=message,
            code="VALIDATION_ERROR",
            details=details,
            actions="Please check your input and try again.",
            status_code=status.HTTP_400_BAD_REQUEST,
            extra_data=extra_data
        )


class AuthenticationException(AgritramAPIException):
    """Exception for authentication errors."""
    
    def __init__(
        self,
        message: str = "Authentication failed",
        details: Optional[str] = None
    ):
        super().__init__(
            message=message,
            code="AUTHENTICATION_ERROR",
            details=details,
            actions="Please log in and try again.",
            status_code=status.HTTP_401_UNAUTHORIZED
        )


class AuthorizationException(AgritramAPIException):
    """Exception for authorization/permission errors."""
    
    def __init__(
        self,
        message: str = "Access denied",
        details: Optional[str] = None
    ):
        super().__init__(
            message=message,
            code="AUTHORIZATION_ERROR",
            details=details,
            actions="Contact support if you believe this is an error.",
            status_code=status.HTTP_403_FORBIDDEN
        )


class ResourceNotFoundException(AgritramAPIException):
    """Exception for resource not found errors."""
    
    def __init__(
        self,
        message: str = "Resource not found",
        details: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None
    ):
        extra_data = {}
        if resource_type:
            extra_data["resource_type"] = resource_type
        if resource_id:
            extra_data["resource_id"] = resource_id
            
        super().__init__(
            message=message,
            code="NOT_FOUND_ERROR",
            details=details,
            status_code=status.HTTP_404_NOT_FOUND,
            extra_data=extra_data
        )


class BusinessLogicException(AgritramAPIException):
    """Exception for business logic violations."""
    
    def __init__(
        self,
        message: str = "Business logic error",
        details: Optional[str] = None
    ):
        super().__init__(
            message=message,
            code="BUSINESS_LOGIC_ERROR",
            details=details,
            actions="Please check your request and try again.",
            status_code=status.HTTP_400_BAD_REQUEST
        )


class DuplicateResourceException(AgritramAPIException):
    """Exception for duplicate resource errors."""
    
    def __init__(
        self,
        message: str = "Resource already exists",
        details: Optional[str] = None,
        resource_type: Optional[str] = None
    ):
        extra_data = {}
        if resource_type:
            extra_data["resource_type"] = resource_type
            
        super().__init__(
            message=message,
            code="DUPLICATE_RESOURCE",
            details=details,
            actions="Use a different identifier or update the existing resource.",
            status_code=status.HTTP_409_CONFLICT,
            extra_data=extra_data
        )


class InvalidTokenException(AgritramAPIException):
    """Exception for invalid token errors."""
    
    def __init__(
        self,
        message: str = "Invalid token",
        details: Optional[str] = None,
        token_type: Optional[str] = None
    ):
        extra_data = {}
        if token_type:
            extra_data["token_type"] = token_type
            
        super().__init__(
            message=message,
            code="INVALID_TOKEN",
            details=details,
            actions="Please refresh your token or log in again.",
            status_code=status.HTTP_401_UNAUTHORIZED,
            extra_data=extra_data
        )


class ExpiredTokenException(AgritramAPIException):
    """Exception for expired token errors."""
    
    def __init__(
        self,
        message: str = "Token has expired",
        details: Optional[str] = None,
        token_type: Optional[str] = None
    ):
        extra_data = {}
        if token_type:
            extra_data["token_type"] = token_type
            
        super().__init__(
            message=message,
            code="EXPIRED_TOKEN",
            details=details,
            actions="Please refresh your token or log in again.",
            status_code=status.HTTP_401_UNAUTHORIZED,
            extra_data=extra_data
        )


class RateLimitException(AgritramAPIException):
    """Exception for rate limit errors."""
    
    def __init__(
        self,
        message: str = "Rate limit exceeded",
        details: Optional[str] = None,
        retry_after: Optional[int] = None
    ):
        extra_data = {}
        if retry_after:
            extra_data["retry_after"] = retry_after
            
        actions = f"Please wait {retry_after} seconds before trying again." if retry_after else "Please wait before trying again."
        
        super().__init__(
            message=message,
            code="RATE_LIMIT_ERROR",
            details=details,
            actions=actions,
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            extra_data=extra_data
        )


class ExternalServiceException(AgritramAPIException):
    """Exception for external service errors."""
    
    def __init__(
        self,
        message: str = "External service error",
        details: Optional[str] = None,
        service_name: Optional[str] = None
    ):
        extra_data = {}
        if service_name:
            extra_data["service"] = service_name
            
        super().__init__(
            message=message,
            code="EXTERNAL_SERVICE_ERROR",
            details=details,
            actions="Please try again later or contact support.",
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            extra_data=extra_data
        )


class DeviceVerificationRequiredException(AgritramAPIException):
    """Exception for device verification required scenarios."""
    
    def __init__(
        self,
        message: str = "Device verification required",
        details: Optional[str] = None,
        device_id: Optional[str] = None
    ):
        extra_data = {
            "verification_required": True
        }
        if device_id:
            extra_data["device_id"] = device_id
            
        super().__init__(
            message=message,
            code="DEVICE_VERIFICATION_REQUIRED",
            details=details,
            actions="Please check your email for verification code.",
            status_code=status.HTTP_202_ACCEPTED,
            extra_data=extra_data
        )


class InsufficientPermissionsException(AgritramAPIException):
    """Exception for insufficient permissions errors."""
    
    def __init__(
        self,
        message: str = "Insufficient permissions",
        details: Optional[str] = None,
        required_permission: Optional[str] = None
    ):
        extra_data = {}
        if required_permission:
            extra_data["required_permission"] = required_permission
            
        super().__init__(
            message=message,
            code="INSUFFICIENT_PERMISSIONS",
            details=details,
            actions="Contact your administrator to request the necessary permissions.",
            status_code=status.HTTP_403_FORBIDDEN,
            extra_data=extra_data
        )


# Convenience functions for raising common exceptions
def raise_validation_error(message: str, details: Optional[str] = None, field_errors: Optional[Dict[str, Any]] = None):
    """Raise a validation exception."""
    # IN USE
    raise ValidationException(message=message, details=details, field_errors=field_errors)


def raise_authentication_error(message: str = "Authentication failed", details: Optional[str] = None):
    """Raise an authentication exception."""
    raise AuthenticationException(message=message, details=details)


def raise_authorization_error(message: str = "Access denied", details: Optional[str] = None):
    """Raise an authorization exception."""
    raise AuthorizationException(message=message, details=details)


def raise_not_found_error(message: str = "Resource not found", details: Optional[str] = None, resource_type: Optional[str] = None, resource_id: Optional[str] = None):
    """Raise a resource not found exception."""
    raise ResourceNotFoundException(message=message, details=details, resource_type=resource_type, resource_id=resource_id)


def raise_business_logic_error(message: str, details: Optional[str] = None):
    """Raise a business logic exception."""
    raise BusinessLogicException(message=message, details=details)
