#!/usr/bin/env python3
"""
Security Fixes Verification Script
Tests the implemented security measures
"""

import requests
import json
import time
import hashlib
import secrets
from urllib.parse import urljoin

class SecurityTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
    
    def log_test(self, test_name, passed, details=""):
        """Log test results"""
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status}: {test_name}")
        if details:
            print(f"   Details: {details}")
        
        self.test_results.append({
            "test": test_name,
            "passed": passed,
            "details": details
        })
    
    def test_device_id_validation(self):
        """Test device ID validation"""
        print("\n🔍 Testing Device ID Validation...")
        
        # Test with invalid device ID (too short)
        try:
            response = self.session.post(
                urljoin(self.base_url, "/user/login/"),
                json={
                    "email": "<EMAIL>",
                    "password": "testpass",
                    "device_id": "short"  # Too short
                }
            )
            
            if response.status_code == 400:
                self.log_test("Device ID length validation", True, "Rejected short device ID")
            else:
                self.log_test("Device ID length validation", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_test("Device ID length validation", False, f"Error: {str(e)}")
    
    def test_device_fingerprint_generation(self):
        """Test device fingerprint generation (frontend simulation)"""
        print("\n🔍 Testing Device Fingerprint Generation...")
        
        # Simulate device fingerprint generation
        try:
            # Simulate browser characteristics
            characteristics = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "en-US",
                "en-US,en;q=0.9",
                "Win32",
                "8",
                "1920x1080",
                "24",
                "-480",
                "true",
                "",
                "canvas_fingerprint_data",
                "webgl_vendor|webgl_renderer",
                "audio_fingerprint_data"
            ]
            
            fingerprint_data = "|".join(characteristics)
            fingerprint = hashlib.sha256(fingerprint_data.encode()).hexdigest()[:16]
            
            if len(fingerprint) == 16:
                self.log_test("Device fingerprint generation", True, f"Generated: {fingerprint}")
            else:
                self.log_test("Device fingerprint generation", False, "Invalid fingerprint length")
                
        except Exception as e:
            self.log_test("Device fingerprint generation", False, f"Error: {str(e)}")
    
    def test_otp_rate_limiting(self):
        """Test OTP resend rate limiting"""
        print("\n🔍 Testing OTP Rate Limiting...")
        
        try:
            # First OTP request
            response1 = self.session.post(
                urljoin(self.base_url, "/user/resend-device-otp/"),
                json={
                    "email": "<EMAIL>",
                    "device_id": "test_device_123456789"
                }
            )
            
            # Immediate second request (should be rate limited)
            response2 = self.session.post(
                urljoin(self.base_url, "/user/resend-device-otp/"),
                json={
                    "email": "<EMAIL>",
                    "device_id": "test_device_123456789"
                }
            )
            
            if response2.status_code == 400:
                self.log_test("OTP rate limiting", True, "Rate limiting working")
            else:
                self.log_test("OTP rate limiting", False, f"Status: {response2.status_code}")
                
        except Exception as e:
            self.log_test("OTP rate limiting", False, f"Error: {str(e)}")
    
    def test_request_signing(self):
        """Test request signing implementation"""
        print("\n🔍 Testing Request Signing...")
        
        try:
            # Simulate request signing
            method = "POST"
            url = "/user/login/"
            body = json.dumps({"email": "<EMAIL>", "password": "test"})
            timestamp = int(time.time() * 1000)
            nonce = secrets.token_hex(16)
            device_id = "test_device_123456789"
            
            # Create signature string
            string_to_sign = f"{method}\n{url}\n{body}\n{timestamp}\n{nonce}\n{device_id}"
            signature = hashlib.sha256(string_to_sign.encode()).hexdigest()
            
            headers = {
                'X-Request-Timestamp': str(timestamp),
                'X-Request-Nonce': nonce,
                'X-Request-Signature': signature,
                'X-Device-ID': device_id,
                'Content-Type': 'application/json'
            }
            
            if all(headers.values()):
                self.log_test("Request signing", True, "Signature generated successfully")
            else:
                self.log_test("Request signing", False, "Missing signature components")
                
        except Exception as e:
            self.log_test("Request signing", False, f"Error: {str(e)}")
    
    def test_security_headers(self):
        """Test security headers in responses"""
        print("\n🔍 Testing Security Headers...")
        
        try:
            response = self.session.get(urljoin(self.base_url, "/"))
            
            security_headers = [
                'X-Frame-Options',
                'X-Content-Type-Options',
                'X-XSS-Protection'
            ]
            
            found_headers = []
            for header in security_headers:
                if header in response.headers:
                    found_headers.append(header)
            
            if found_headers:
                self.log_test("Security headers", True, f"Found: {', '.join(found_headers)}")
            else:
                self.log_test("Security headers", False, "No security headers found")
                
        except Exception as e:
            self.log_test("Security headers", False, f"Error: {str(e)}")
    
    def test_device_security_middleware(self):
        """Test device security middleware"""
        print("\n🔍 Testing Device Security Middleware...")
        
        try:
            # Test request without device headers
            response = self.session.get(
                urljoin(self.base_url, "/user/profile/"),
                headers={'Authorization': 'Bearer invalid_token'}
            )
            
            # Should get 403 for missing device info or 401 for invalid token
            if response.status_code in [401, 403]:
                self.log_test("Device security middleware", True, f"Status: {response.status_code}")
            else:
                self.log_test("Device security middleware", False, f"Unexpected status: {response.status_code}")
                
        except Exception as e:
            self.log_test("Device security middleware", False, f"Error: {str(e)}")
    
    def test_authentication_bypass_prevention(self):
        """Test authentication bypass prevention"""
        print("\n🔍 Testing Authentication Bypass Prevention...")
        
        try:
            # Attempt login without OTP for new device
            response = self.session.post(
                urljoin(self.base_url, "/user/login/"),
                json={
                    "email": "<EMAIL>",
                    "password": "testpass",
                    "device_id": f"new_device_{secrets.token_hex(8)}",
                    "device_name": "Test Device"
                }
            )
            
            # Should require device verification (403) or user not found (400/401)
            if response.status_code in [400, 401, 403]:
                self.log_test("Authentication bypass prevention", True, f"Status: {response.status_code}")
            else:
                self.log_test("Authentication bypass prevention", False, f"Unexpected status: {response.status_code}")
                
        except Exception as e:
            self.log_test("Authentication bypass prevention", False, f"Error: {str(e)}")
    
    def run_all_tests(self):
        """Run all security tests"""
        print("🚀 Starting Security Fixes Verification")
        print("=" * 50)
        
        self.test_device_id_validation()
        self.test_device_fingerprint_generation()
        self.test_otp_rate_limiting()
        self.test_request_signing()
        self.test_security_headers()
        self.test_device_security_middleware()
        self.test_authentication_bypass_prevention()
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Test Summary")
        print("=" * 50)
        
        passed = sum(1 for result in self.test_results if result["passed"])
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 All security tests passed!")
        else:
            print(f"\n⚠️  {total - passed} test(s) failed. Review implementation.")
        
        return passed == total

def main():
    """Main test runner"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test security fixes implementation")
    parser.add_argument("--url", default="http://localhost:8000", help="Backend URL")
    args = parser.parse_args()
    
    tester = SecurityTester(args.url)
    success = tester.run_all_tests()
    
    exit(0 if success else 1)

if __name__ == "__main__":
    main()
