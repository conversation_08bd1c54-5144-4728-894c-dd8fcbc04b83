# Generated by Django 5.2 on 2025-06-23 08:45

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="DeviceToken",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("device_id", models.CharField(max_length=255, unique=True)),
                ("device_name", models.CharField(max_length=255)),
                (
                    "device_type",
                    models.CharField(
                        choices=[
                            ("web", "Web Browser"),
                            ("mobile", "Mobile App"),
                            ("desktop", "Desktop App"),
                            ("api", "API Client"),
                        ],
                        max_length=50,
                    ),
                ),
                ("is_trusted", models.BooleanField(default=False)),
                ("trust_expires", models.DateTimeField(blank=True, null=True)),
                (
                    "fingerprint",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("first_seen", models.DateTimeField(auto_now_add=True)),
                ("last_seen", models.DateTimeField(auto_now=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True, null=True)),
                ("is_blocked", models.BooleanField(default=False)),
                ("blocked_at", models.DateTimeField(blank=True, null=True)),
                ("blocked_reason", models.TextField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="device_tokens",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Device Token",
                "verbose_name_plural": "Device Tokens",
                "indexes": [
                    models.Index(
                        fields=["user", "device_id"],
                        name="oauth2_auth_user_id_e0385e_idx",
                    ),
                    models.Index(
                        fields=["device_id"], name="oauth2_auth_device__5c2ed4_idx"
                    ),
                    models.Index(
                        fields=["is_trusted"], name="oauth2_auth_is_trus_47cc77_idx"
                    ),
                ],
                "unique_together": {("user", "device_id")},
            },
        ),
        migrations.CreateModel(
            name="SecurityEvent",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "event_type",
                    models.CharField(
                        choices=[
                            ("login", "Login"),
                            ("logout", "Logout"),
                            ("token_issued", "Token Issued"),
                            ("token_refreshed", "Token Refreshed"),
                            ("token_revoked", "Token Revoked"),
                            ("failed_login", "Failed Login"),
                            ("suspicious_activity", "Suspicious Activity"),
                            ("device_registered", "Device Registered"),
                            ("device_blocked", "Device Blocked"),
                            ("password_changed", "Password Changed"),
                        ],
                        max_length=50,
                    ),
                ),
                ("description", models.TextField()),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True, null=True)),
                ("device_id", models.CharField(blank=True, max_length=255, null=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="security_events",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Security Event",
                "verbose_name_plural": "Security Events",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "event_type"],
                        name="oauth2_auth_user_id_85e8db_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="oauth2_auth_created_769adb_idx"
                    ),
                    models.Index(
                        fields=["event_type"], name="oauth2_auth_event_t_e7c769_idx"
                    ),
                ],
            },
        ),
    ]
