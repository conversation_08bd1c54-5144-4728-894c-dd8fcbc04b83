#!/usr/bin/env python
"""
Test script to verify that the fingerprint and logging fixes are working correctly
"""

import os
import sys
import django
from unittest.mock import Mock, patch

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agritram.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    django.setup()
    print("✅ Django setup successful")
except Exception as e:
    print(f"❌ Django setup failed: {e}")
    sys.exit(1)

def test_generate_device_fingerprint_without_unique_id():
    """Test that generate_device_fingerprint works without unique_id parameter"""
    try:
        from oauth2_auth.utils import generate_device_fingerprint
        from django.test import RequestFactory
        
        factory = RequestFactory()
        request = factory.post('/test/', {})
        request.META['HTTP_USER_AGENT'] = 'Test User Agent'
        request.META['HTTP_ACCEPT_LANGUAGE'] = 'en-US,en;q=0.9'
        request.META['HTTP_ACCEPT_ENCODING'] = 'gzip, deflate'
        
        # This should work without unique_id parameter
        fingerprint = generate_device_fingerprint(request)
        
        print(f"✅ generate_device_fingerprint works without unique_id: {fingerprint[:20]}...")
        return True
        
    except Exception as e:
        print(f"❌ generate_device_fingerprint test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_generate_device_fingerprint_with_unique_id():
    """Test that generate_device_fingerprint works with unique_id parameter"""
    try:
        from oauth2_auth.utils import generate_device_fingerprint
        from django.test import RequestFactory
        
        factory = RequestFactory()
        request = factory.post('/test/', {})
        request.META['HTTP_USER_AGENT'] = 'Test User Agent'
        request.META['HTTP_ACCEPT_LANGUAGE'] = 'en-US,en;q=0.9'
        request.META['HTTP_ACCEPT_ENCODING'] = 'gzip, deflate'
        
        # This should work with unique_id parameter
        fingerprint = generate_device_fingerprint(request, "test-unique-id")
        
        print(f"✅ generate_device_fingerprint works with unique_id: {fingerprint[:20]}...")
        return True
        
    except Exception as e:
        print(f"❌ generate_device_fingerprint with unique_id test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_log_security_event_without_unique_id():
    """Test that log_security_event works without unique_id parameter"""
    try:
        from oauth2_auth.utils import log_security_event
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Mock user
        user = Mock()
        user.id = 1
        user.email = "<EMAIL>"
        
        # This should work without unique_id parameter
        result = log_security_event(
            event_type="test_event",
            description="Test security event",
            user=user,
            ip_address="127.0.0.1",
            user_agent="Test Agent"
        )
        
        print("✅ log_security_event works without unique_id")
        return True
        
    except Exception as e:
        print(f"❌ log_security_event test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_log_security_event_with_unique_id():
    """Test that log_security_event works with unique_id parameter"""
    try:
        from oauth2_auth.utils import log_security_event
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Mock user
        user = Mock()
        user.id = 1
        user.email = "<EMAIL>"
        
        # This should work with unique_id parameter
        result = log_security_event(
            unique_id="test-unique-id",
            event_type="test_event",
            description="Test security event",
            user=user,
            ip_address="127.0.0.1",
            user_agent="Test Agent"
        )
        
        print("✅ log_security_event works with unique_id")
        return True
        
    except Exception as e:
        print(f"❌ log_security_event with unique_id test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_register_device_function():
    """Test that register_device function works correctly"""
    try:
        from oauth2_auth.utils import register_device
        from django.test import RequestFactory
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Mock user
        user = Mock()
        user.id = 1
        user.email = "<EMAIL>"
        
        factory = RequestFactory()
        request = factory.post('/test/', {})
        request.META['HTTP_USER_AGENT'] = 'Test User Agent'
        request.META['HTTP_ACCEPT_LANGUAGE'] = 'en-US,en;q=0.9'
        request.META['HTTP_ACCEPT_ENCODING'] = 'gzip, deflate'
        request.META['REMOTE_ADDR'] = '127.0.0.1'
        
        with patch('oauth2_auth.utils.DeviceToken') as mock_device_token, \
             patch('oauth2_auth.utils.log_security_event') as mock_log_security:
            
            mock_device_token.objects.get_or_create.return_value = (Mock(), True)
            
            # This should work without errors
            result = register_device(
                user=user,
                device_id="test-device-id",
                device_name="Test Device",
                device_type="web",
                request=request
            )
            
            print("✅ register_device function works correctly")
            return True
        
    except Exception as e:
        print(f"❌ register_device test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all fingerprint fix tests"""
    print("🧪 Testing Fingerprint and Logging Fixes")
    print("=" * 50)
    
    tests = [
        test_generate_device_fingerprint_without_unique_id,
        test_generate_device_fingerprint_with_unique_id,
        test_log_security_event_without_unique_id,
        test_log_security_event_with_unique_id,
        test_register_device_function,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\n🔍 Running {test.__name__}...")
        if test():
            passed += 1
        else:
            print(f"❌ {test.__name__} failed")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All fingerprint and logging fix tests passed!")
        return True
    else:
        print("❌ Some tests failed. Please check the issues above.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
