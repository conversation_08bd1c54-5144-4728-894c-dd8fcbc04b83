import React, { useState, useEffect, useRef } from 'react'
import { Button } from './ui/button'
import { useToast } from './ui/use-toast'
import OTPService from '../services/otpService'

interface OTPVerificationProps {
  email: string
  password: string
  deviceId: string
  deviceName: string
  onSuccess: (loginResponse: any) => void
  onCancel: () => void
}

const OTPVerification: React.FC<OTPVerificationProps> = ({
  email,
  password,
  deviceId,
  deviceName,
  onSuccess,
  onCancel
}) => {
  const [otp, setOtp] = useState(['', '', '', '', '', ''])
  const [isLoading, setIsLoading] = useState(false)
  const [isResending, setIsResending] = useState(false)
  const [resendCooldown, setResendCooldown] = useState(0)
  const { toast } = useToast()
  const inputRefs = useRef<(HTMLInputElement | null)[]>([])

  // Cooldown timer for resend
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => setResendCooldown(resendCooldown - 1), 1000)
      return () => clearTimeout(timer)
    }
  }, [resendCooldown])

  const handleOtpChange = (index: number, value: string) => {
    // Only allow digits
    if (!/^\d*$/.test(value)) return

    const newOtp = [...otp]
    newOtp[index] = value.slice(-1) // Only take the last character

    setOtp(newOtp)

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus()
    }

    // Auto-submit when all fields are filled
    if (newOtp.every(digit => digit !== '') && newOtp.join('').length === 6) {
      handleVerifyOtp(newOtp.join(''))
    }
  }

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus()
    }
  }

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault()
    const pastedData = e.clipboardData.getData('text').replace(/\D/g, '').slice(0, 6)
    
    if (pastedData.length === 6) {
      const newOtp = pastedData.split('')
      setOtp(newOtp)
      
      // Auto-submit pasted OTP
      handleVerifyOtp(pastedData)
    }
  }

  const handleVerifyOtp = async (otpCode?: string) => {
    const otpToVerify = otpCode || otp.join('')
    
    if (!OTPService.isOTPComplete(otpToVerify)) {
      toast({
        title: 'Invalid OTP',
        description: 'Please enter a complete 6-digit verification code.',
        variant: 'destructive'
      })
      return
    }

    setIsLoading(true)

    try {
      const loginResponse = await OTPService.verifyOTPAndLogin({
        email,
        password,
        device_id: deviceId,
        device_name: deviceName,
        otp_code: otpToVerify
      })

      toast({
        title: 'Success!',
        description: 'Device verified successfully. You are now logged in.',
      })

      onSuccess(loginResponse)
    } catch (error: any) {
      console.error('OTP verification error:', error)
      
      // Clear OTP on error
      setOtp(['', '', '', '', '', ''])
      inputRefs.current[0]?.focus()
      
      toast({
        title: 'Verification Failed',
        description: error.message || 'Invalid verification code. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleResendOtp = async () => {
    if (resendCooldown > 0) return

    setIsResending(true)

    try {
      await OTPService.resendOTP(email, deviceId)
      
      toast({
        title: 'OTP Resent',
        description: 'A new verification code has been sent to your email.',
      })
      
      setResendCooldown(60) // 60 second cooldown
      setOtp(['', '', '', '', '', '']) // Clear current OTP
      inputRefs.current[0]?.focus()
    } catch (error: any) {
      console.error('Resend OTP error:', error)
      toast({
        title: 'Resend Failed',
        description: error.message || 'Failed to resend verification code. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsResending(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-primary-text mb-2">Device Verification</h2>
        <p className="text-primary-text/70 mb-4">
          We've detected a new device. Please enter the 6-digit verification code sent to your email.
        </p>
        <p className="text-sm text-primary-text/60">
          Device: <span className="font-medium">{deviceName}</span>
        </p>
      </div>

      <div className="space-y-4">
        <div className="flex justify-center space-x-2">
          {otp.map((digit, index) => (
            <input
              key={index}
              ref={el => inputRefs.current[index] = el}
              type="text"
              inputMode="numeric"
              maxLength={1}
              value={digit}
              onChange={e => handleOtpChange(index, e.target.value)}
              onKeyDown={e => handleKeyDown(index, e)}
              onPaste={index === 0 ? handlePaste : undefined}
              className="w-12 h-12 text-center text-lg font-bold border border-border-color rounded-lg bg-input-bg text-primary-text focus:outline-none focus:ring-2 focus:ring-button-bg focus:border-transparent"
              disabled={isLoading}
            />
          ))}
        </div>

        <div className="flex flex-col space-y-3">
          <Button
            onClick={() => handleVerifyOtp()}
            disabled={isLoading || !OTPService.isOTPComplete(otp.join(''))}
            className="w-full"
          >
            {isLoading ? 'Verifying...' : 'Verify Device'}
          </Button>

          <div className="flex justify-between items-center text-sm">
            <button
              onClick={handleResendOtp}
              disabled={isResending || resendCooldown > 0}
              className="text-link-text hover:text-link-text/80 disabled:text-primary-text/40 disabled:cursor-not-allowed"
            >
              {isResending ? 'Resending...' : 
               resendCooldown > 0 ? `Resend in ${resendCooldown}s` : 
               'Resend Code'}
            </button>

            <button
              onClick={onCancel}
              disabled={isLoading}
              className="text-primary-text/60 hover:text-primary-text disabled:cursor-not-allowed"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">Security Notice</h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>
                This verification helps protect your account from unauthorized access. 
                The code will expire in 10 minutes.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default OTPVerification
