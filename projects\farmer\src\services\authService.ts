import { LoginResponse, TokenData } from '../stores/authStore'
import apiClient from './apiClient'
import DeviceService, { DeviceInfo } from './deviceService'
import OTPService, { OTPResponse, LoginWithOTPResponse } from './otpService'

export const login = async (email: string, password: string): Promise<LoginResponse> => {
  try {
    // Get device information
    const deviceInfo = DeviceService.getDeviceInfo()

    // First, try to login and check if OTP is required
    const otpResponse = await OTPService.initiateLogin(
      email,
      password,
      deviceInfo.deviceId,
      deviceInfo.deviceName
    )

    if (otpResponse.requires_otp) {
      // Throw a specific error that the frontend can catch to show OTP form
      const error = new Error('Device verification required') as any
      error.requiresOTP = true
      error.deviceId = otpResponse.device_id || deviceInfo.deviceId
      error.deviceName = deviceInfo.deviceName
      throw error
    }

    // If no OTP required, make the actual login request with device info
    const response = await apiClient.post('/user/login/', {
      email,
      password,
      device_id: deviceInfo.deviceId,
      device_name: deviceInfo.deviceName,
      device_fingerprint: deviceInfo.fingerprint,
      device_info: {
        user_agent: deviceInfo.userAgent,
        platform: deviceInfo.platform,
        language: deviceInfo.language,
        timezone: deviceInfo.timezone,
        screen_resolution: deviceInfo.screenResolution
      }
    })

    const data = response.data.data || response.data

    if (data && data.access_token && data.user) {
      // Return the complete login response with JWT tokens
      return {
        token: data.access_token, // For backward compatibility
        access_token: data.access_token,
        refresh_token: data.refresh_token,
        token_type: data.token_type || 'Bearer',
        expires_in: data.expires_in,
        refresh_expires_in: data.refresh_expires_in,
        message: response.data.success?.message || 'Login successful',
        user: data.user,
      }
    } else {
      throw new Error('Login response data is incomplete or missing required fields.')
    }
  } catch (error) {
    console.error('Error during login:', error)
    throw error
  }
}

// New function for OTP-based login
export const loginWithOTP = async (
  email: string,
  password: string,
  deviceId: string,
  deviceName: string,
  otpCode: string
): Promise<LoginResponse> => {
  try {
    const response = await OTPService.verifyOTPAndLogin({
      email,
      password,
      device_id: deviceId,
      device_name: deviceName,
      otp_code: otpCode
    })

    return response
  } catch (error) {
    console.error('Error during OTP login:', error)
    throw error
  }
}

export const refreshToken = async (refresh_token: string): Promise<TokenData> => {
  try {
    const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}/user/refresh-token/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        refresh_token,
      }),
    })

    if (!response.ok) {
      throw new Error('Token refresh failed')
    }

    const data = await response.json()

    // Calculate expiration timestamps
    const now = Date.now()
    const expires_at = now + (data.expires_in * 1000)
    const refresh_expires_at = now + (data.refresh_expires_in * 1000)

    return {
      access_token: data.access_token,
      refresh_token: data.refresh_token,
      token_type: data.token_type,
      expires_in: data.expires_in,
      refresh_expires_in: data.refresh_expires_in,
      expires_at,
      refresh_expires_at,
    }
  } catch (error) {
    console.error('Error refreshing token:', error)
    throw error
  }
}

export const registerUser = async (data: { name: string; email: string; password: string }) => {
  try {
    // Get device information for registration
    const deviceInfo = DeviceService.getDeviceInfo()

    const response = await apiClient.post('/user/register/', {
      name: data.name,
      email: data.email,
      password: data.password,
      device_id: deviceInfo.deviceId,
      device_name: deviceInfo.deviceName,
      device_fingerprint: deviceInfo.fingerprint,
      device_info: {
        user_agent: deviceInfo.userAgent,
        platform: deviceInfo.platform,
        language: deviceInfo.language,
        timezone: deviceInfo.timezone,
        screen_resolution: deviceInfo.screenResolution
      }
      // Role is now auto-detected by backend based on request origin
    })

    return response.data
  } catch (error) {
    console.error('Error during registration:', error)
    throw error
  }
}

export const logout = async (refresh_token?: string): Promise<void> => {
  try {
    if (refresh_token) {
      // Call logout endpoint to invalidate tokens on server
      await apiClient.post('/user/logout/', {
        refresh_token,
      })
    }
  } catch (error) {
    // Don't throw error for logout - we want to clear local state regardless
    console.error('Error during logout:', error)
  }
}
