# Generated by Django 5.2 on 2025-06-24 10:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("user", "0003_user_failed_login_attempts_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="is_banned",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="user",
            name="is_deleted",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="user",
            name="is_mail_verified",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="user",
            name="is_online",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="user",
            name="is_suspended",
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name="user",
            name="is_active",
            field=models.<PERSON>oleanField(default=True),
        ),
        migrations.<PERSON><PERSON><PERSON>ield(
            model_name="user",
            name="is_staff",
            field=models.<PERSON>oleanField(default=False),
        ),
    ]
