import logging
from rest_framework.decorators import (
    api_view,
    permission_classes,
    authentication_classes,
)
from rest_framework.authentication import Session<PERSON>uthentication, TokenAuthentication
from oauth2_provider.contrib.rest_framework import OAuth2Authentication
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from .models import CropTransfer, Crops
from .serializers import (
    CropTransferSerializer,
    CropsSerializer,
    CropTransferHistorySerializer,
)
from django.db import models
from oauth2_auth.permissions import AnyRolePermission
from rest_framework import permissions
from user.permissions import UserStatusMixin

logger = logging.getLogger(__name__)
from agritram.message_utils import (
    handle_serializer_errors,
    handle_exception_with_logging,
    StandardSuccessResponse,
)


class CropViewPermission(permissions.BasePermission, UserStatusMixin):
    """
    Custom permission for crop operations based on business rules.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            # Generate unique ID for logging
            logger.warning(
                f"Crop view permission denied for {request.user.email}",
                extra={
                    "operation": "CROP_PERMISSION_DENIED",
                    "user_email": request.user.email,
                    "user_role": getattr(request.user, "role", "unknown"),
                    "denial_reason": reason,
                    "permission_type": "crop_view",
                },
            )
            return False

        user_role = request.user.role

        # Only farmers, traders, and manufacturers can view crops
        return user_role in ["farmer", "trader", "manufacturer", "admin"]


class CropCreatePermission(permissions.BasePermission):
    """
    Permission for creating crops.
    Business Rule: Traders can create crops they receive from farmers.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        user_role = request.user.role

        # Only traders and admins can create crops
        return user_role in ["trader", "admin"]


class CropTransferPermission(permissions.BasePermission):
    """
    Permission for creating crop transfers.
    Business Rule: Traders and manufacturers can create crop transfers.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        user_role = request.user.role

        # Only traders, manufacturers, and admins can create crop transfers
        return user_role in ["trader", "manufacturer", "admin"]


@api_view(["POST"])
@permission_classes(
    [CropTransferPermission]
)  # Traders and manufacturers can create crop transfers
@authentication_classes(
    [OAuth2Authentication, SessionAuthentication, TokenAuthentication]
)
def create_crop_transfer(request):
    """
    Create a new crop transfer record.
    Business Rule: Traders and manufacturers can create crop transfers.
    """

    try:
        # Log incoming request
        logger.info(
            f"Create crop transfer request: {request.method} {request.path}",
            extra={"operation": "CREATE_CROP_TRANSFER_REQUEST"},
        )

        logger.info(
            "Starting crop transfer creation",
            extra={
                "operation": "CROP_TRANSFER_CREATION_START",
                "operation_type": "crop_transfer_creation",
            },
        )

        serializer = CropTransferSerializer(data=request.data)
        if serializer.is_valid():
            transfer = serializer.save()

            logger.info(
                f"Crop transfer created successfully with ID {transfer.id}",
                extra={
                    "operation": "CROP_TRANSFER_CREATED",
                    "transfer_id": transfer.id,
                    "operation_result": "success",
                },
            )

            # Log successful response
            logger.info(
                "Crop transfer creation response",
                extra={
                    "operation": "CREATE_CROP_TRANSFER_SUCCESS",
                    "status_code": 201,
                    "message": "Crop transfer created successfully",
                    "transfer_id": transfer.id,
                },
            )

            return StandardSuccessResponse.record_created(
                message="Crop transfer created successfully",
                details=f"New crop transfer record created with ID: {transfer.id}",
                record_data=CropTransferSerializer(transfer).data,
            )
        else:
            logger.warning(
                "Crop transfer creation failed due to validation errors",
                extra={
                    "operation": "CROP_TRANSFER_VALIDATION_ERROR",
                    "validation_errors": serializer.errors,
                    "operation_result": "validation_failed",
                },
            )
            return handle_serializer_errors(serializer)
    except Exception as e:
        return handle_exception_with_logging(
            e, "crop transfer creation", request, request.user
        )


@api_view(["GET"])
@permission_classes(
    [CropViewPermission]
)  # Farmers, traders, and manufacturers can view crops
@authentication_classes(
    [OAuth2Authentication, SessionAuthentication, TokenAuthentication]
)
def get_crop(request, crop_id):
    """
    Retrieve a crop by its crop_id.
    Business Rule: Only farmers, traders, and manufacturers can view crops.
    """

    try:
        # Log incoming request
        logger.info(
            f"Get crop request: {request.method} {request.path}",
            extra={"operation": "GET_CROP_REQUEST", "crop_id": crop_id},
        )

        logger.info(
            f"Retrieving crop with ID {crop_id}",
            extra={
                "operation": "CROP_RETRIEVAL",
                "crop_id": crop_id,
                "operation_type": "crop_retrieval",
            },
        )

        crop = get_object_or_404(Crops, crop_id=crop_id)
        serializer = CropsSerializer(crop)

        # Log successful response
        logger.info(
            "Crop retrieval response",
            extra={
                "operation": "GET_CROP_SUCCESS",
                "status_code": 200,
                "message": "Crop retrieved successfully",
                "crop_id": crop_id,
            },
        )

        return StandardSuccessResponse.data_retrieved(
            message="Crop retrieved successfully",
            details=f"Retrieved crop information for crop ID: {crop_id}",
            data=serializer.data,
        )
    except Exception as e:
        return handle_exception_with_logging(e, "crop retrieval", request, request.user)


@api_view(["GET"])
@permission_classes(
    [CropViewPermission]
)  # Farmers, traders, and manufacturers can view crops
@authentication_classes(
    [OAuth2Authentication, SessionAuthentication, TokenAuthentication]
)
def get_all_crops(request):
    """
    Retrieve all crops.
    Business Rule: Only farmers, traders, and manufacturers can view crops.
    """

    try:
        # Log incoming request
        logger.info(
            f"Get all crops request: {request.method} {request.path}",
            extra={"operation": "GET_ALL_CROPS_REQUEST"},
        )

        logger.info(
            "Retrieving all crops",
            extra={
                "operation": "ALL_CROPS_RETRIEVAL",
                "operation_type": "all_crops_retrieval",
            },
        )

        crops = Crops.objects.all()
        serializer = CropsSerializer(crops, many=True)

        # Log successful response
        logger.info(
            "All crops retrieval response",
            extra={
                "operation": "GET_ALL_CROPS_SUCCESS",
                "status_code": 200,
                "message": "All crops retrieved successfully",
                "crops_count": len(serializer.data),
            },
        )

        return StandardSuccessResponse.data_retrieved(
            message="All crops retrieved successfully",
            details=f"Retrieved {len(serializer.data)} crop records",
            data=serializer.data,
        )
    except Exception as e:
        return handle_exception_with_logging(
            e, "crops retrieval", request, request.user
        )


@api_view(["GET"])
@permission_classes(
    [AnyRolePermission]
)  # Anyone authenticated can view crop transfer history
@authentication_classes(
    [OAuth2Authentication, SessionAuthentication, TokenAuthentication]
)
def get_crop_transaction_history(request, crop_id):
    """
    Retrieve transaction history for a specific crop.
    Business Rule: Anyone authenticated can view crop transfer history.
    """

    try:
        # Log incoming request
        logger.info(
            f"Get crop transaction history request: {request.method} {request.path}",
            extra={
                "operation": "GET_CROP_TRANSACTION_HISTORY_REQUEST",
                "crop_id": crop_id,
            },
        )

        logger.info(
            f"Retrieving transaction history for crop {crop_id}",
            extra={
                "operation": "CROP_TRANSACTION_HISTORY_RETRIEVAL",
                "crop_id": crop_id,
                "operation_type": "crop_transaction_history_retrieval",
            },
        )

        transfers = CropTransfer.objects.filter(crop__crop_id=crop_id).order_by(
            "timestamp"
        )
        serializer = CropTransferHistorySerializer(transfers, many=True)

        # Log successful response
        logger.info(
            "Crop transaction history retrieval response",
            extra={
                "operation": "GET_CROP_TRANSACTION_HISTORY_SUCCESS",
                "status_code": 200,
                "message": "Crop transaction history retrieved successfully",
                "crop_id": crop_id,
                "transactions_count": len(serializer.data),
            },
        )

        return StandardSuccessResponse.data_retrieved(
            message="Crop transaction history retrieved successfully",
            details=f"Retrieved {len(serializer.data)} transaction records for crop ID: {crop_id}",
            data=serializer.data,
        )
    except Exception as e:
        return handle_exception_with_logging(
            e, "crop transaction history retrieval", request, request.user
        )
