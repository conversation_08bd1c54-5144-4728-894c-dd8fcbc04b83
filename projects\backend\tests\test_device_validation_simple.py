"""
Simple test cases for device validation during registration
"""

from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.core.cache import cache

from oauth2_auth.models import DeviceToken
from oauth2_auth.authentication import DeviceAuthenticationService

User = get_user_model()


class SimpleDeviceValidationTest(TestCase):
    """Simple test for device validation service"""

    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()
        cache.clear()
        
        # Create a test user with existing device
        self.existing_user = User.objects.create_user(
            email="<EMAIL>",
            name="Existing User",
            password="testpass123"
        )
        
        # Create an existing device
        self.existing_device = DeviceToken.objects.create(
            user=self.existing_user,
            device_id="existing_device_12345678901234567890123456789012345678901234567890",
            device_name="Existing Device",
            device_type="web",
            fingerprint="existing_fingerprint",
            ip_address="*************",
            user_agent="Mozilla/5.0 Test Browser"
        )

    def test_device_validation_new_device(self):
        """Test device validation service with new device"""
        request = self.factory.post('/register/', {
            'device_id': 'new_device_12345678901234567890123456789012345678901234567890'
        })
        request.META['REMOTE_ADDR'] = '*************'
        request.META['HTTP_USER_AGENT'] = 'Mozilla/5.0 New Browser'
        
        result = DeviceAuthenticationService.validate_device_for_registration(
            device_id='new_device_12345678901234567890123456789012345678901234567890',
            device_type='web',
            request=request
        )
        
        self.assertTrue(result['is_valid'])
        self.assertEqual(result['message'], 'Device validation passed')
        self.assertTrue(result['details']['is_new_device'])
        self.assertEqual(len(result['warnings']), 0)

    def test_device_validation_existing_device_same_type(self):
        """Test device validation service with existing device and same type"""
        request = self.factory.post('/register/', {
            'device_id': self.existing_device.device_id
        })
        request.META['REMOTE_ADDR'] = '*************'  # Same IP
        request.META['HTTP_USER_AGENT'] = 'Mozilla/5.0 Test Browser'  # Same UA
        
        result = DeviceAuthenticationService.validate_device_for_registration(
            device_id=self.existing_device.device_id,
            device_type='web',  # Same type
            request=request
        )
        
        self.assertTrue(result['is_valid'])
        self.assertEqual(result['message'], 'Device validation passed')
        self.assertFalse(result['details'].get('is_new_device', False))
        # May have warnings due to fingerprint changes, but should be valid

    def test_device_validation_existing_device_different_type(self):
        """Test device validation service with existing device but different type"""
        request = self.factory.post('/register/', {
            'device_id': self.existing_device.device_id
        })
        request.META['REMOTE_ADDR'] = '*************'
        request.META['HTTP_USER_AGENT'] = 'Mozilla/5.0 Test Browser'
        
        result = DeviceAuthenticationService.validate_device_for_registration(
            device_id=self.existing_device.device_id,
            device_type='mobile',  # Different type
            request=request
        )
        
        self.assertFalse(result['is_valid'])
        self.assertEqual(result['message'], 'Device type mismatch')
        self.assertIn('device_type_mismatch', result['details'])
        self.assertEqual(result['details']['device_type_mismatch']['stored'], 'web')
        self.assertEqual(result['details']['device_type_mismatch']['provided'], 'mobile')

    def test_device_validation_existing_device_ip_change(self):
        """Test device validation service with existing device but different IP"""
        request = self.factory.post('/register/', {
            'device_id': self.existing_device.device_id
        })
        request.META['REMOTE_ADDR'] = '*************'  # Different IP
        request.META['HTTP_USER_AGENT'] = 'Mozilla/5.0 Test Browser'
        
        result = DeviceAuthenticationService.validate_device_for_registration(
            device_id=self.existing_device.device_id,
            device_type='web',
            request=request
        )
        
        self.assertTrue(result['is_valid'])  # IP change is warning only
        self.assertIn('ip_address_changed', result['warnings'])
        self.assertIn('ip_change', result['details'])
        self.assertEqual(result['details']['ip_change']['stored'], '*************')
        self.assertEqual(result['details']['ip_change']['current'], '*************')

    def test_device_validation_result_structure(self):
        """Test that validation result has correct structure"""
        request = self.factory.post('/register/', {
            'device_id': 'test_device_12345678901234567890123456789012345678901234567890'
        })
        request.META['REMOTE_ADDR'] = '*************'
        request.META['HTTP_USER_AGENT'] = 'Mozilla/5.0 Test Browser'
        
        result = DeviceAuthenticationService.validate_device_for_registration(
            device_id='test_device_12345678901234567890123456789012345678901234567890',
            device_type='web',
            request=request
        )
        
        # Check required keys
        self.assertIn('is_valid', result)
        self.assertIn('message', result)
        self.assertIn('details', result)
        self.assertIn('warnings', result)
        
        # Check types
        self.assertIsInstance(result['is_valid'], bool)
        self.assertIsInstance(result['message'], str)
        self.assertIsInstance(result['details'], dict)
        self.assertIsInstance(result['warnings'], list)

    def tearDown(self):
        """Clean up test data"""
        cache.clear()
        User.objects.all().delete()
        DeviceToken.objects.all().delete()
