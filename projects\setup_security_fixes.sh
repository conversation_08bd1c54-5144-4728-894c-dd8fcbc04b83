#!/bin/bash

# Security Fixes Setup Script
# Helps deploy and verify the security implementations

set -e

echo "🔐 Agritram Security Fixes Setup"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -d "projects" ]; then
    print_error "Please run this script from the root directory containing the 'projects' folder"
    exit 1
fi

print_status "Checking project structure..."

# Check backend
if [ ! -d "projects/backend" ]; then
    print_error "Backend directory not found"
    exit 1
fi

# Check frontend projects
FRONTEND_PROJECTS=("farmer" "trader" "manufacturer")
for project in "${FRONTEND_PROJECTS[@]}"; do
    if [ ! -d "projects/$project" ]; then
        print_warning "Frontend project '$project' not found"
    else
        print_success "Found frontend project: $project"
    fi
done

print_status "Setting up backend security fixes..."

# Backend setup
cd projects/backend

# Check if virtual environment exists
if [ ! -d "venv" ] && [ ! -d ".venv" ]; then
    print_warning "No virtual environment found. Creating one..."
    python3 -m venv venv
    source venv/bin/activate
else
    print_status "Activating virtual environment..."
    if [ -d "venv" ]; then
        source venv/bin/activate
    else
        source .venv/bin/activate
    fi
fi

# Install dependencies (if needed)
print_status "Checking Python dependencies..."
pip install -q django djangorestframework django-cors-headers django-oauth-toolkit

# Run migrations
print_status "Running database migrations..."
python manage.py makemigrations --noinput || print_warning "No new migrations to create"
python manage.py migrate --noinput || print_error "Migration failed"

# Collect static files (if needed)
if [ -f "manage.py" ]; then
    print_status "Collecting static files..."
    python manage.py collectstatic --noinput --clear || print_warning "Static files collection failed"
fi

cd ../..

# Frontend setup
for project in "${FRONTEND_PROJECTS[@]}"; do
    if [ -d "projects/$project" ]; then
        print_status "Setting up frontend project: $project"
        cd "projects/$project"
        
        # Check if package.json exists
        if [ -f "package.json" ]; then
            # Install dependencies if node_modules doesn't exist
            if [ ! -d "node_modules" ]; then
                print_status "Installing npm dependencies for $project..."
                npm install || print_warning "npm install failed for $project"
            else
                print_success "Dependencies already installed for $project"
            fi
            
            # Check if our new services exist
            if [ -f "src/services/deviceService.ts" ]; then
                print_success "Device service found in $project"
            else
                print_warning "Device service not found in $project"
            fi
            
            if [ -f "src/services/otpService.ts" ]; then
                print_success "OTP service found in $project"
            else
                print_warning "OTP service not found in $project"
            fi
        else
            print_warning "No package.json found in $project"
        fi
        
        cd ../..
    fi
done

# Security verification
print_status "Running security verification..."

# Check if test script exists and run it
if [ -f "projects/test_security_fixes.py" ]; then
    print_status "Running security tests..."
    cd projects
    python3 test_security_fixes.py --url http://localhost:8000 || print_warning "Some security tests failed"
    cd ..
else
    print_warning "Security test script not found"
fi

# Configuration checks
print_status "Checking security configuration..."

# Check Django settings
SETTINGS_FILE="projects/backend/agritram/settings.py"
if [ -f "$SETTINGS_FILE" ]; then
    if grep -q "DeviceSecurityMiddleware" "$SETTINGS_FILE"; then
        print_success "Device security middleware configured"
    else
        print_warning "Device security middleware not found in settings"
    fi
    
    if grep -q "SECURE_BROWSER_XSS_FILTER" "$SETTINGS_FILE"; then
        print_success "Security headers configured"
    else
        print_warning "Security headers not configured"
    fi
else
    print_error "Django settings file not found"
fi

# Environment variables check
print_status "Checking environment variables..."

ENV_VARS=("SECRET_KEY" "DEBUG" "ALLOWED_HOSTS")
for var in "${ENV_VARS[@]}"; do
    if [ -n "${!var}" ]; then
        print_success "Environment variable $var is set"
    else
        print_warning "Environment variable $var is not set"
    fi
done

# Security recommendations
print_status "Security recommendations:"
echo ""
echo "1. 🔒 Ensure HTTPS is enabled in production"
echo "2. 🔑 Use strong, unique SECRET_KEY in production"
echo "3. 📧 Configure email service for OTP delivery"
echo "4. 🛡️  Set up proper CORS configuration"
echo "5. 📊 Monitor security events in logs"
echo "6. 🔄 Regularly rotate device IDs (30-day cycle)"
echo "7. 🚨 Set up alerts for security violations"
echo ""

# Final status
print_success "Security fixes setup completed!"
print_status "Next steps:"
echo "1. Start the backend server: cd projects/backend && python manage.py runserver"
echo "2. Start frontend projects: cd projects/[farmer|trader|manufacturer] && npm run dev"
echo "3. Test the OTP flow with a new device"
echo "4. Monitor security logs for any issues"
echo ""

print_status "For detailed information, see: projects/SECURITY_FIXES_SUMMARY.md"

exit 0
