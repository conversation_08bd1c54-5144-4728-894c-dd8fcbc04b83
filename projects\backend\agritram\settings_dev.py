"""
Development settings with enhanced logging and colorlog support.

To use this settings file, set the environment variable:
DJANGO_SETTINGS_MODULE=agritram.settings_dev

Or run Django commands with:
python manage.py runserver --settings=agritram.settings_dev
"""

from .settings import *
import os

# Override DEBUG to ensure it's True for development
DEBUG = True

# Enhanced logging configuration for development with colorlog
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "detailed": {
            "format": "{levelname} | {asctime} | {name} | {filename}:{lineno} | {funcName} | CID:{correlation_id} | {message}",
            "style": "{",
        },
        "colored": {
            "()": "colorlog.ColoredFormatter",
            "format": "{log_color}{levelname:<8}{reset} | {cyan}{asctime}{reset} | {blue}{filename}:{lineno}{reset} | {purple}CID:{correlation_id}{reset} | {message}",
            "style": "{",
            "log_colors": {
                "DEBUG": "cyan",
                "INFO": "green",
                "WARNING": "yellow", 
                "ERROR": "red",
                "CRITICAL": "red,bg_white",
            },
        },
        "simple_colored": {
            "()": "colorlog.ColoredFormatter",
            "format": "{log_color}{levelname:<8}{reset} | {cyan}{asctime}{reset} | {blue}{filename}:{lineno}{reset} | {message}",
            "style": "{",
            "log_colors": {
                "DEBUG": "cyan",
                "INFO": "green", 
                "WARNING": "yellow",
                "ERROR": "red",
                "CRITICAL": "red,bg_white",
            },
        },
    },
    "filters": {
        "correlation_id": {
            "()": "agritram.correlation_id_middleware.CorrelationIdLoggingFilter",
        },
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "colored",
            "filters": ["correlation_id"],
        },
        "console_simple": {
            "level": "DEBUG", 
            "class": "logging.StreamHandler",
            "formatter": "simple_colored",
        },
        "file": {
            "level": "DEBUG",
            "class": "logging.FileHandler",
            "filename": "logs/development.log",
            "formatter": "detailed",
            "filters": ["correlation_id"],
        },
        "api_file": {
            "level": "DEBUG",
            "class": "logging.FileHandler",
            "filename": "logs/api_dev.log",
            "formatter": "detailed",
            "filters": ["correlation_id"],
        },
        "error_file": {
            "level": "ERROR",
            "class": "logging.FileHandler",
            "filename": "logs/errors_dev.log",
            "formatter": "detailed",
            "filters": ["correlation_id"],
        },
    },
    "loggers": {
        "oauth2_auth": {
            "handlers": ["console", "file"],
            "level": "DEBUG",
            "propagate": True,
        },
        "security": {
            "handlers": ["console", "file"],
            "level": "DEBUG",
            "propagate": True,
        },
        "agritram": {
            "handlers": ["console", "api_file"],
            "level": "DEBUG",
            "propagate": True,
        },
        "user": {
            "handlers": ["console", "api_file"],
            "level": "DEBUG",
            "propagate": True,
        },
        "user.services": {
            "handlers": ["console", "api_file"],
            "level": "DEBUG",
            "propagate": True,
        },
        "user.services.registration_orchestrator": {
            "handlers": ["console", "api_file"],
            "level": "DEBUG",
            "propagate": True,
        },
        "django": {
            "handlers": ["console", "error_file"],
            "level": "INFO",
            "propagate": False,
        },
        # Root logger for everything else
        "": {
            "handlers": ["console", "file"],
            "level": "DEBUG",
        },
    },
}

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

print("🎨 Development settings loaded with colorlog support!")
print("📝 Enhanced logging enabled for all user services")
print("📁 Logs will be written to logs/development.log and logs/api_dev.log")
