/**
 * Security Service for Request Signing and MITM Protection
 * Implements request signing to prevent man-in-the-middle attacks
 */

interface RequestSignature {
  timestamp: number
  nonce: string
  signature: string
}

class SecurityService {
  private static readonly SIGNATURE_VALIDITY_WINDOW = 300000 // 5 minutes in milliseconds
  
  /**
   * Generate a cryptographically secure nonce
   */
  private static generateNonce(): string {
    const array = new Uint8Array(16)
    crypto.getRandomValues(array)
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }

  /**
   * Create HMAC-SHA256 signature
   */
  private static async createSignature(
    method: string,
    url: string,
    body: string,
    timestamp: number,
    nonce: string,
    deviceId: string
  ): Promise<string> {
    // Create the string to sign
    const stringToSign = `${method}\n${url}\n${body}\n${timestamp}\n${nonce}\n${deviceId}`
    
    // Use device ID as a simple key (in production, use a proper key derivation)
    const key = await crypto.subtle.importKey(
      'raw',
      new TextEncoder().encode(deviceId),
      { name: 'HM<PERSON>', hash: 'SHA-256' },
      false,
      ['sign']
    )
    
    const signature = await crypto.subtle.sign('HMAC', key, new TextEncoder().encode(stringToSign))
    return Array.from(new Uint8Array(signature), byte => byte.toString(16).padStart(2, '0')).join('')
  }

  /**
   * Sign a request to prevent tampering
   */
  static async signRequest(
    method: string,
    url: string,
    body: any,
    deviceId: string
  ): Promise<RequestSignature> {
    const timestamp = Date.now()
    const nonce = this.generateNonce()
    const bodyString = typeof body === 'string' ? body : JSON.stringify(body || {})
    
    const signature = await this.createSignature(method, url, bodyString, timestamp, nonce, deviceId)
    
    return {
      timestamp,
      nonce,
      signature
    }
  }

  /**
   * Validate request signature timing
   */
  static isSignatureValid(timestamp: number): boolean {
    const now = Date.now()
    const age = now - timestamp
    return age >= 0 && age <= this.SIGNATURE_VALIDITY_WINDOW
  }

  /**
   * Encrypt sensitive data for transmission
   */
  static async encryptSensitiveData(data: string, deviceId: string): Promise<string> {
    try {
      // Simple XOR encryption with device ID (for demonstration)
      // In production, use proper encryption like AES-GCM
      const key = deviceId.repeat(Math.ceil(data.length / deviceId.length)).slice(0, data.length)
      let encrypted = ''
      
      for (let i = 0; i < data.length; i++) {
        encrypted += String.fromCharCode(data.charCodeAt(i) ^ key.charCodeAt(i))
      }
      
      return btoa(encrypted)
    } catch (error) {
      console.error('Encryption error:', error)
      return data // Fallback to unencrypted
    }
  }

  /**
   * Decrypt sensitive data
   */
  static async decryptSensitiveData(encryptedData: string, deviceId: string): Promise<string> {
    try {
      const encrypted = atob(encryptedData)
      const key = deviceId.repeat(Math.ceil(encrypted.length / deviceId.length)).slice(0, encrypted.length)
      let decrypted = ''
      
      for (let i = 0; i < encrypted.length; i++) {
        decrypted += String.fromCharCode(encrypted.charCodeAt(i) ^ key.charCodeAt(i))
      }
      
      return decrypted
    } catch (error) {
      console.error('Decryption error:', error)
      return encryptedData // Fallback
    }
  }

  /**
   * Generate secure headers for API requests
   */
  static async generateSecureHeaders(
    method: string,
    url: string,
    body: any,
    deviceId: string,
    deviceFingerprint: string
  ): Promise<Record<string, string>> {
    const signature = await this.signRequest(method, url, body, deviceId)
    
    return {
      'X-Request-Timestamp': signature.timestamp.toString(),
      'X-Request-Nonce': signature.nonce,
      'X-Request-Signature': signature.signature,
      'X-Device-ID': deviceId,
      'X-Device-Fingerprint': deviceFingerprint,
      'X-Client-Version': '1.0.0',
      'X-Security-Level': 'high'
    }
  }

  /**
   * Validate response integrity
   */
  static validateResponseIntegrity(response: any, expectedSignature?: string): boolean {
    // Basic response validation
    if (!response || typeof response !== 'object') {
      return false
    }
    
    // Check for required security headers in response
    const requiredFields = ['timestamp', 'data']
    for (const field of requiredFields) {
      if (!(field in response)) {
        console.warn(`Missing required field in response: ${field}`)
        return false
      }
    }
    
    // Validate response timestamp (should be recent)
    if (response.timestamp) {
      const responseTime = new Date(response.timestamp).getTime()
      const now = Date.now()
      const age = now - responseTime
      
      if (age > this.SIGNATURE_VALIDITY_WINDOW) {
        console.warn('Response timestamp is too old')
        return false
      }
    }
    
    return true
  }

  /**
   * Detect potential security threats
   */
  static detectSecurityThreats(): {
    threats: string[]
    riskLevel: 'low' | 'medium' | 'high'
  } {
    const threats: string[] = []
    let riskLevel: 'low' | 'medium' | 'high' = 'low'

    // Check for developer tools
    if (window.outerHeight - window.innerHeight > 200 || window.outerWidth - window.innerWidth > 200) {
      threats.push('Developer tools may be open')
      riskLevel = 'medium'
    }

    // Check for suspicious user agent
    const ua = navigator.userAgent
    if (!ua || ua.length < 50 || /bot|crawler|spider/i.test(ua)) {
      threats.push('Suspicious user agent detected')
      riskLevel = 'high'
    }

    // Check for missing security features
    if (!window.crypto || !window.crypto.getRandomValues) {
      threats.push('Missing cryptographic capabilities')
      riskLevel = 'high'
    }

    // Check for insecure context
    if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
      threats.push('Insecure connection detected')
      riskLevel = 'high'
    }

    // Check for suspicious timing
    const performanceEntries = performance.getEntriesByType('navigation')
    if (performanceEntries.length > 0) {
      const navigation = performanceEntries[0] as PerformanceNavigationTiming
      if (navigation.loadEventEnd - navigation.navigationStart < 100) {
        threats.push('Suspiciously fast page load')
        riskLevel = 'medium'
      }
    }

    return { threats, riskLevel }
  }

  /**
   * Generate integrity hash for critical data
   */
  static async generateIntegrityHash(data: any): Promise<string> {
    const dataString = JSON.stringify(data)
    const encoder = new TextEncoder()
    const dataBuffer = encoder.encode(dataString)
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  }

  /**
   * Validate data integrity
   */
  static async validateIntegrity(data: any, expectedHash: string): Promise<boolean> {
    const actualHash = await this.generateIntegrityHash(data)
    return actualHash === expectedHash
  }

  /**
   * Secure storage with encryption
   */
  static secureStore(key: string, value: any, deviceId: string): void {
    try {
      const dataString = JSON.stringify(value)
      const encrypted = btoa(dataString) // Simple encoding (use proper encryption in production)
      localStorage.setItem(`secure_${key}`, encrypted)
    } catch (error) {
      console.error('Secure storage error:', error)
    }
  }

  /**
   * Secure retrieval with decryption
   */
  static secureRetrieve(key: string, deviceId: string): any {
    try {
      const encrypted = localStorage.getItem(`secure_${key}`)
      if (!encrypted) return null
      
      const dataString = atob(encrypted) // Simple decoding
      return JSON.parse(dataString)
    } catch (error) {
      console.error('Secure retrieval error:', error)
      return null
    }
  }

  /**
   * Clear all secure storage
   */
  static clearSecureStorage(): void {
    const keys = Object.keys(localStorage)
    keys.forEach(key => {
      if (key.startsWith('secure_')) {
        localStorage.removeItem(key)
      }
    })
  }
}

export default SecurityService
export type { RequestSignature }
