import AuthLayout from '@/components/AuthLayout'
import { useToast } from '@/hooks/use-toast'
import apiClient from '@/services/apiClient'
import axios from 'axios'
import { useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'

const ActivateAccount = () => {
  const { toast } = useToast()
  const navigate = useNavigate()
  const { uid, token } = useParams()
  const [isActivating, setIsActivating] = useState(true)

  useEffect(() => {
    const activateAccount = async () => {
      try {
        console.log('Activating account with params:', uid, token)

        // Try GET request first (direct URL access from email)
        let response
        try {
          response = await apiClient.get(`/user/activate-account/${uid}/${token}/`)
        } catch (getError) {
          // If GET fails, try POST (fallback)
          console.log('GET activation failed, trying POST:', getError)
          response = await apiClient.post('/user/activate-account/', { uid, token })
        }

        toast({
          title: 'Account Activated!',
          description: 'Your account has been successfully activated. Redirecting to login...',
        })
        setIsActivating(false)
        setTimeout(() => {
          navigate('/login')
        }, 2000)
      } catch (error) {
        console.error('Account activation error:', error)
        if (axios.isAxiosError(error)) {
          const errorMessage = error.response?.data?.error?.message ||
                              error.response?.data?.error ||
                              error.response?.data?.message ||
                              'Failed to activate account'
          toast({
            title: 'Activation Failed',
            description: errorMessage,
            variant: 'destructive',
          })
        } else {
          toast({
            title: 'Activation Failed',
            description: 'Failed to activate account',
            variant: 'destructive',
          })
        }
        setIsActivating(false)
      }
    }

    if (uid && token) {
      activateAccount()
    }
  }, [uid, token, navigate, toast])

  return (
    <AuthLayout
      title="Account Activation"
      subtitle={isActivating ? 'Please wait while we activate your account...' : 'Account activation failed'}
    >
      <div className="flex flex-col items-center justify-center space-y-4">
        {isActivating ? (
          <div className="animate-pulse text-button-bg">Activating your account...</div>
        ) : (
          <button
            onClick={() => navigate('/login')}
            className="w-full py-3 px-4 bg-button-bg text-button-text rounded-lg font-medium hover:bg-button-bg-hover transition-colors"
          >
            Return to Login
          </button>
        )}
      </div>
    </AuthLayout>
  )
}

export default ActivateAccount
