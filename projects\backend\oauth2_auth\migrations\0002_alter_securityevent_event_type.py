# Generated by Django 5.2 on 2025-06-23 11:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("oauth2_auth", "0001_initial"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="securityevent",
            name="event_type",
            field=models.CharField(
                choices=[
                    ("login", "Login"),
                    ("logout", "Logout"),
                    ("failed_login", "Failed Login"),
                    ("account_locked", "Account Locked"),
                    ("account_unlocked", "Account Unlocked"),
                    ("token_issued", "Token Issued"),
                    ("token_refreshed", "Token Refreshed"),
                    ("token_revoked", "Token Revoked"),
                    ("token_expired", "Token Expired"),
                    ("invalid_token_usage", "Invalid Token Usage"),
                    ("token_ip_change", "Token IP Change"),
                    ("device_registered", "Device Registered"),
                    ("device_verified", "Device Verified"),
                    ("device_blocked", "Device Blocked"),
                    ("device_unblocked", "Device Unblocked"),
                    ("new_device_detected", "New Device Detected"),
                    ("device_fingerprint_changed", "Device Fingerprint Changed"),
                    ("suspicious_activity", "Suspicious Activity"),
                    ("brute_force_attempt", "Brute Force Attempt"),
                    ("rate_limit_exceeded", "Rate Limit Exceeded"),
                    ("ip_blocked", "IP Blocked"),
                    ("security_alert_sent", "Security Alert Sent"),
                    ("user_registered", "User Registered"),
                    ("account_activated", "Account Activated"),
                    ("password_changed", "Password Changed"),
                    ("password_reset_requested", "Password Reset Requested"),
                    ("password_reset_completed", "Password Reset Completed"),
                    ("email_changed", "Email Changed"),
                    ("profile_updated", "Profile Updated"),
                    ("otp_generated", "OTP Generated"),
                    ("otp_verified", "OTP Verified"),
                    ("invalid_otp", "Invalid OTP"),
                    ("oauth2_client_created", "OAuth2 Client Created"),
                    ("oauth2_client_revoked", "OAuth2 Client Revoked"),
                    ("client_secret_rotated", "Client Secret Rotated"),
                    ("authorization_granted", "Authorization Granted"),
                    ("authorization_denied", "Authorization Denied"),
                    ("admin_login", "Admin Login"),
                    ("admin_action", "Admin Action"),
                    ("user_impersonation", "User Impersonation"),
                    ("bulk_operation", "Bulk Operation"),
                    ("system_maintenance", "System Maintenance"),
                    ("api_access", "API Access"),
                    ("api_rate_limit", "API Rate Limit"),
                    ("api_error", "API Error"),
                    ("endpoint_access", "Endpoint Access"),
                    ("access_denied", "Access Denied"),
                    ("data_export", "Data Export"),
                    ("data_import", "Data Import"),
                    ("data_deletion", "Data Deletion"),
                    ("sensitive_data_access", "Sensitive Data Access"),
                ],
                max_length=50,
            ),
        ),
    ]
