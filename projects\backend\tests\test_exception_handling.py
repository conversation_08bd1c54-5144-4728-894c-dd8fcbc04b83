#!/usr/bin/env python
"""
Test script to verify exception handling in registration orchestrator
"""

import os
import sys
import django
from unittest.mock import Mock, patch

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agritram.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    django.setup()
    print("✅ Django setup successful")
except Exception as e:
    print(f"❌ Django setup failed: {e}")
    sys.exit(1)

def test_exception_handling_before_device_data():
    """Test that exception handling works when device_data is not yet defined"""
    try:
        from user.services.registration_orchestrator import RegistrationOrchestrator
        from django.test import RequestFactory
        from agritram.exceptions import ValidationException
        
        factory = RequestFactory()
        orchestrator = RegistrationOrchestrator()
        
        # Create a request that will fail during validation
        request = factory.post('/register/', {
            'email': '<EMAIL>',
            'name': 'Test User',
            'password': 'testpass123'
        })
        request.data = request.POST
        
        # Mock the validation service to raise an exception before device_data is set
        with patch.object(orchestrator.validation_service, 'validate_registration_request') as mock_validate:
            mock_validate.side_effect = ValidationException("Rate limit exceeded")
            
            try:
                orchestrator.register_user(request)
                print("❌ Expected exception was not raised")
                return False
            except ValidationException:
                print("✅ Exception handling works correctly when device_data is not defined")
                return True
            except Exception as e:
                print(f"❌ Unexpected exception type: {e}")
                return False
                
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_exception_handling_after_device_data():
    """Test that exception handling works when device_data is defined"""
    try:
        from user.services.registration_orchestrator import RegistrationOrchestrator
        from django.test import RequestFactory
        from agritram.exceptions import DuplicateResourceException
        
        factory = RequestFactory()
        orchestrator = RegistrationOrchestrator()
        
        # Create a request
        request = factory.post('/register/', {
            'email': '<EMAIL>',
            'name': 'Test User',
            'password': 'testpass123'
        })
        request.data = request.POST
        
        # Mock the validation service to return device_data, then user service to fail
        device_data = {
            'email': '<EMAIL>',
            'device_id': 'test-device-id',
            'device_name': 'Test Device'
        }
        
        with patch.object(orchestrator.validation_service, 'validate_registration_request') as mock_validate, \
             patch.object(orchestrator.user_service, 'handle_user_creation_flow') as mock_user_create:
            
            mock_validate.return_value = device_data
            mock_user_create.side_effect = DuplicateResourceException("User already exists")
            
            try:
                orchestrator.register_user(request)
                print("❌ Expected exception was not raised")
                return False
            except DuplicateResourceException:
                print("✅ Exception handling works correctly when device_data is defined")
                return True
            except Exception as e:
                print(f"❌ Unexpected exception type: {e}")
                return False
                
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run exception handling tests"""
    print("🧪 Testing Exception Handling in Registration Orchestrator")
    print("=" * 60)
    
    tests = [
        test_exception_handling_before_device_data,
        test_exception_handling_after_device_data,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\n🔍 Running {test.__name__}...")
        if test():
            passed += 1
        else:
            print(f"❌ {test.__name__} failed")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All exception handling tests passed!")
        return True
    else:
        print("❌ Some tests failed. Please check the issues above.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
