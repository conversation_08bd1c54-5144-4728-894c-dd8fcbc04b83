#!/usr/bin/env python
"""
Simple test script to verify the registration refactoring works correctly
"""

import os
import sys
import django
from unittest.mock import Mock, patch

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "agritram.settings")
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    django.setup()
    print("✅ Django setup successful")
except Exception as e:
    print(f"❌ Django setup failed: {e}")
    sys.exit(1)


def test_import_services():
    """Test that all services can be imported correctly"""
    try:
        from user.services.registration_validation_service import (
            RegistrationValidationService,
        )
        from user.services.device_management_service import DeviceManagementService
        from user.services.user_creation_service import UserCreationService
        from user.services.activation_service import ActivationService
        from user.services.registration_orchestrator import RegistrationOrchestrator

        print("✅ All services imported successfully")
        return True
    except Exception as e:
        print(f"❌ Service import failed: {e}")
        return False


def test_service_methods():
    """Test that service methods exist and are callable"""
    try:
        from user.services.registration_validation_service import (
            RegistrationValidationService,
        )

        # Check that the new method names exist
        assert hasattr(
            RegistrationValidationService, "extract_and_validate_device_data"
        )
        assert hasattr(
            RegistrationValidationService, "validate_device_against_database"
        )
        assert hasattr(RegistrationValidationService, "validate_duplicate_user")
        assert hasattr(RegistrationValidationService, "validate_registration_request")

        print("✅ All validation service methods exist")

        from user.services.device_management_service import DeviceManagementService

        assert hasattr(DeviceManagementService, "generate_device_id")
        assert hasattr(DeviceManagementService, "extract_device_info")
        assert hasattr(DeviceManagementService, "register_device")
        assert hasattr(DeviceManagementService, "handle_device_registration_flow")

        print("✅ All device management service methods exist")

        from user.services.user_creation_service import UserCreationService

        assert hasattr(UserCreationService, "prepare_registration_data")
        assert hasattr(UserCreationService, "create_user")
        assert hasattr(UserCreationService, "create_oauth2_application")
        assert hasattr(UserCreationService, "handle_user_creation_flow")

        print("✅ All user creation service methods exist")

        from user.services.activation_service import ActivationService

        assert hasattr(ActivationService, "generate_activation_token")
        assert hasattr(ActivationService, "construct_activation_url")
        assert hasattr(ActivationService, "send_activation_email")
        assert hasattr(ActivationService, "handle_activation_flow")

        print("✅ All activation service methods exist")

        from user.services.registration_orchestrator import RegistrationOrchestrator

        assert hasattr(RegistrationOrchestrator, "register_user")

        print("✅ Registration orchestrator methods exist")

        return True
    except Exception as e:
        print(f"❌ Service method check failed: {e}")
        return False


def test_device_data_validation():
    """Test the improved device data validation logic"""
    try:
        from user.services.registration_validation_service import (
            RegistrationValidationService,
        )
        from django.test import RequestFactory

        factory = RequestFactory()
        service = RegistrationValidationService()

        # Test with user-provided device_id (must be 58+ chars)
        device_id_58_chars = "20240101120000_" + "a" * 44  # 14 + 1 + 44 = 59 chars
        request = factory.post(
            "/register/",
            {
                "email": "<EMAIL>",
                "device_id": device_id_58_chars,
                "device_type": "web",
            },
        )
        # Add data attribute for compatibility
        request.data = request.POST

        with patch(
            "user.services.registration_validation_service.get_dynamic_device_info"
        ) as mock_device_info:
            mock_device_info.return_value = {
                "device_name": "Test Device",
                "device_type": "web",
            }

            # Debug: Check the provided device_id length
            print(
                f"Provided device_id: {device_id_58_chars} (length: {len(device_id_58_chars)})"
            )

            result = service.extract_and_validate_device_data(request, "test-id")

            # Verify the new field is present
            assert "user_provided_device_id" in result
            assert result["user_provided_device_id"] == True
            assert result["email"] == "<EMAIL>"
            assert result["device_type"] == "web"

        print("✅ Device data validation with user-provided device_id works")

        # Test with system-generated device_id
        request2 = factory.post(
            "/register/", {"email": "<EMAIL>", "name": "Test User"}
        )
        # Add data attribute for compatibility
        request2.data = request2.POST

        with patch(
            "user.services.registration_validation_service.get_dynamic_device_info"
        ) as mock_device_info, patch("secrets.token_urlsafe") as mock_secrets, patch(
            "user.services.registration_validation_service.timezone"
        ) as mock_timezone:

            mock_device_info.return_value = {
                "device_name": "Test Device",
                "device_type": "web",
            }
            mock_timezone.now.return_value.strftime.return_value = "20240101120000"
            mock_secrets.return_value = (
                "a" * 44
            )  # This creates 14 + 1 + 44 = 59 chars (58+ required)

            result2 = service.extract_and_validate_device_data(request2, "test-id")

            # Debug: Check the generated device_id length
            generated_device_id = result2["device_id"]
            print(
                f"Generated device_id: {generated_device_id} (length: {len(generated_device_id)})"
            )

            # Verify the new field is present and correct
            assert "user_provided_device_id" in result2
            assert result2["user_provided_device_id"] == False
            assert result2["email"] == "<EMAIL>"

        print("✅ Device data validation with system-generated device_id works")

        return True
    except Exception as e:
        print(f"❌ Device data validation test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_database_validation_logic():
    """Test the improved database validation logic"""
    try:
        from user.services.registration_validation_service import (
            RegistrationValidationService,
        )
        from django.test import RequestFactory

        factory = RequestFactory()
        service = RegistrationValidationService()

        # Test that DB validation is called for user-provided device_id
        device_data_user_provided = {
            "device_id": "user-provided-id",
            "device_name": "Test Device",
            "device_type": "web",
            "detected_device_type": "web",
            "provided_device_type": "web",
            "email": "<EMAIL>",
            "user_provided_device_id": True,
        }

        request = factory.post("/register/", {"device_id": "user-provided-id"})
        request.data = request.POST

        with patch(
            "user.services.registration_validation_service.DeviceAuthenticationService"
        ) as mock_device_auth, patch(
            "user.services.registration_validation_service.log_operation_info"
        ) as mock_log:

            mock_device_auth.validate_device_for_registration.return_value = {
                "is_valid": True,
                "warnings": [],
                "details": {},
            }

            # Should not raise exception
            service.validate_device_against_database(
                device_data_user_provided, request, "test-id"
            )

            # Verify DB validation was called
            mock_device_auth.validate_device_for_registration.assert_called_once()

        print("✅ Database validation called for user-provided device_id")

        # Test that DB validation is skipped for system-generated device_id
        device_data_system_generated = {
            "device_id": "system-generated-id",
            "device_name": "Test Device",
            "device_type": "web",
            "detected_device_type": "web",
            "provided_device_type": None,
            "email": "<EMAIL>",
            "user_provided_device_id": False,
        }

        request2 = factory.post("/register/", {})
        request2.data = request2.POST

        with patch(
            "user.services.registration_validation_service.DeviceAuthenticationService"
        ) as mock_device_auth, patch(
            "user.services.registration_validation_service.log_operation_info"
        ) as mock_log:

            # Should not raise exception
            service.validate_device_against_database(
                device_data_system_generated, request2, "test-id"
            )

            # Verify DB validation was NOT called
            mock_device_auth.validate_device_for_registration.assert_not_called()

            # Verify skip log was called
            mock_log.assert_any_call(
                "test-id",
                "DEVICE_DB_VALIDATION_SKIPPED",
                "Skipping database validation for generated device: system-generated-id",
                metadata={
                    "device_id": "system-generated-id",
                    "email": "<EMAIL>",
                    "reason": "device_id_generated_by_system",
                },
            )

        print("✅ Database validation skipped for system-generated device_id")

        return True
    except Exception as e:
        print(f"❌ Database validation logic test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """Run all tests"""
    print("🧪 Testing Registration API Refactoring")
    print("=" * 50)

    tests = [
        test_import_services,
        test_service_methods,
        test_device_data_validation,
        test_database_validation_logic,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        print(f"\n🔍 Running {test.__name__}...")
        if test():
            passed += 1
        else:
            print(f"❌ {test.__name__} failed")

    print(f"\n📊 Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Refactoring is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the issues above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
