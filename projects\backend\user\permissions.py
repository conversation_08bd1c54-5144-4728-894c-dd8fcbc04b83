from rest_framework import permissions
import logging, json

logger = logging.getLogger(__name__)


class UserStatusPermission(permissions.BasePermission):
    """
    Base permission class that checks user status fields.
    Ensures users are active, not deleted, and not locked.
    """

    def has_permission(self, request, view):

        if not request.user or not request.user.is_authenticated:
            logger.warning(
                f"SECURITY_EVENT: UNAUTHENTICATED_ACCESS_ATTEMPT | description=Permission denied: User not authenticated | metadata={{'request_path': '{getattr(request, 'path', None)}'}}"
            )
            return False

        user = request.user

        # Check if user is deleted
        if user.is_deleted:
            logger.warning(
                f"SECURITY_EVENT: DELETED_USER_ACCESS_ATTEMPT | description=API access denied for deleted user: {user.email} | user={user.email} | metadata={{'user_status': 'deleted'}}"
            )
            return False

        if not user.is_mail_verified:
            logger.warning(
                f"SECURITY_EVENT: UNVERIFIED_USER_ACCESS_ATTEMPT | description=API access denied for unverified user: {user.email} | user={user.email} | metadata={{'user_status': 'unverified_email'}}"
            )
            return False

        # Check if user is active
        if not user.is_active:
            logger.warning(
                f"SECURITY_EVENT: INACTIVE_USER_ACCESS_ATTEMPT | description=API access denied for inactive user: {user.email} | user={user.email} | metadata={{'user_status': 'inactive'}}"
            )
            return False

        # Check if account is locked
        if user.is_account_locked():
            logger.warning(
                f"SECURITY_EVENT: LOCKED_USER_ACCESS_ATTEMPT | description=API access denied for locked user: {user.email} | user={user.email} | metadata={{'user_status': 'locked'}}"
            )
            return False

        # Log successful permission check
        logger.info(
            f"OPERATION_INFO: USER_STATUS_PERMISSION_GRANTED | message=User status permission granted for user: {user.email} | metadata={{'user_id': {user.id}, 'user_email': '{user.email}', 'user_role': '{getattr(user, 'role', None)}', 'permission_class': '{self.__class__.__name__}'}}"
        )

        return True


class EmailVerifiedPermission(UserStatusPermission):
    """
    Permission that requires email verification in addition to basic user status checks.
    Use for sensitive operations that require verified email.
    """

    def has_permission(self, request, view):
        # First check basic user status
        if not super().has_permission(request, view):
            return False

        # Check email verification
        if not request.user.is_mail_verified:
            logger.warning(
                f"SECURITY_EVENT: EMAIL_VERIFICATION_REQUIRED | description=API access denied for unverified email: {request.user.email} | user={request.user.email} | metadata={{'permission_class': 'EmailVerifiedPermission'}}"
            )
            return False

        # Log successful email verification permission
        logger.info(
            f"OPERATION_INFO: EMAIL_VERIFIED_PERMISSION_GRANTED | message=Email verified permission granted for user: {request.user.email} | metadata={{'user_id': {request.user.id}, 'user_email': '{request.user.email}', 'permission_class': '{self.__class__.__name__}'}}"
        )

        return True


class ActiveUserPermission(UserStatusPermission):
    """
    Permission for operations that require the user to be currently active.
    Extends basic status checks with additional activity requirements.
    Note: Online status tracking has been removed from the User model.
    This class now only performs basic user status validation.
    """

    def has_permission(self, request, view):        
        # First check basic user status
        if not super().has_permission(request, view):
            return False

        # TODO: Note: is_online field has been removed from User model
        # This permission now only validates basic user status
        # If online status tracking is needed, implement it separately

        # Log successful active user permission
        logger.info(
            f"OPERATION_INFO: ACTIVE_USER_PERMISSION_GRANTED | message=Active user permission granted for user: {request.user.email} | metadata={{'user_id': {request.user.id}, 'user_email': '{request.user.email}', 'permission_class': '{self.__class__.__name__}', 'note': 'Online status tracking removed from User model'}}"
        )

        return True


class StaffOrActiveUserPermission(permissions.BasePermission):
    """
    Permission that allows staff users to bypass user status checks,
    but applies full status checks to regular users.
    """

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            logger.warning(
                f"SECURITY_EVENT: UNAUTHENTICATED_STAFF_ACCESS_ATTEMPT | description=Staff or active user permission denied: User not authenticated | metadata={{'request_path': '{getattr(request, 'path', None)}'}}"
            )
            return False

        # Staff users bypass status checks (for admin operations)
        if request.user.is_staff or request.user.is_superuser:
            logger.info(
                f"OPERATION_INFO: STAFF_PERMISSION_GRANTED | message=Staff permission granted for user: {request.user.email} | metadata={{'user_id': {request.user.id}, 'user_email': '{request.user.email}', 'is_staff': {request.user.is_staff}, 'is_superuser': {request.user.is_superuser}, 'permission_class': '{self.__class__.__name__}'}}"
            )
            return True

        # Apply full status checks for regular users
        user_status_permission = UserStatusPermission()
        return user_status_permission.has_permission(request, view)


class VerifiedEmailForSensitiveOpsPermission(permissions.BasePermission):
    """
    Permission for sensitive operations that require verified email.
    Examples: financial transactions, account changes, data exports.
    """

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            logger.warning(
                f"SECURITY_EVENT: UNAUTHENTICATED_SENSITIVE_ACCESS_ATTEMPT | description=Sensitive operation permission denied: User not authenticated | metadata={{'request_path': '{getattr(request, 'path', None)}'}}"
            )
            return False

        # Check basic user status first
        user_status_permission = UserStatusPermission()
        if not user_status_permission.has_permission(request, view):
            return False

        # For write operations, require email verification
        if request.method not in permissions.SAFE_METHODS:
            if not request.user.is_mail_verified:
                logger.warning(
                    f"SECURITY_EVENT: SENSITIVE_OPERATION_UNVERIFIED_EMAIL | description=Sensitive operation denied for unverified email: {request.user.email} | user={request.user.email} | metadata={{'operation_type': 'sensitive_write_operation', 'method': '{request.method}', 'permission_class': '{self.__class__.__name__}'}}"
                )
                return False

        # Log successful sensitive operation permission
        logger.info(
            f"OPERATION_INFO: SENSITIVE_OPERATION_PERMISSION_GRANTED | message=Sensitive operation permission granted for user: {request.user.email} | metadata={{'user_id': {request.user.id}, 'user_email': '{request.user.email}', 'method': '{request.method}', 'is_write_operation': {request.method not in permissions.SAFE_METHODS}, 'permission_class': '{self.__class__.__name__}'}}"
        )

        return True


class UserStatusMixin:
    """
    Mixin to add user status checking to existing permission classes.
    Can be used to enhance existing permissions with status checks.
    """

    def check_user_status(self, user):
        """
        Check if user passes all status requirements.
        Returns tuple (is_valid, reason)
        """
        if user.is_deleted:
            return False, "User account is deleted"

        if not user.is_active:
            return False, "User account is inactive"

        if user.is_account_locked():
            return False, "User account is locked"

        return True, None

    def has_permission_with_status_check(self, request, view):
        """
        Helper method to combine status checks with custom permission logic.
        Override this in your permission class instead of has_permission.
        """
        if not request.user or not request.user.is_authenticated:
            logger.warning(
                f"SECURITY_EVENT: UNAUTHENTICATED_MIXIN_ACCESS_ATTEMPT | description=UserStatusMixin permission denied: User not authenticated | metadata={{'request_path': '{getattr(request, 'path', None)}'}}"
            )
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            logger.warning(
                f"SECURITY_EVENT: USER_STATUS_CHECK_FAILED | description=Permission denied for {request.user.email}: {reason} | user={request.user.email} | metadata={{'failure_reason': '{reason}', 'mixin_class': 'UserStatusMixin'}}"
            )
            return False

        # Log successful status check
        logger.info(
            f"OPERATION_INFO: USER_STATUS_MIXIN_CHECK_PASSED | message=User status mixin check passed for user: {request.user.email} | metadata={{'user_id': {request.user.id}, 'user_email': '{request.user.email}', 'mixin_class': 'UserStatusMixin'}}"
        )

        return True


# Enhanced versions of existing permission classes with status checks
class EnhancedIsAuthenticated(UserStatusPermission):
    """
    Enhanced version of IsAuthenticated that includes user status checks.
    Drop-in replacement for DRF's IsAuthenticated.
    """

    pass


class EnhancedIsAuthenticatedOrReadOnly(permissions.BasePermission):
    """
    Enhanced version that allows read-only access to anyone,
    but requires authenticated users with valid status for write operations.
    """

    def has_permission(self, request, view):
        # Read permissions for any request
        if request.method in permissions.SAFE_METHODS:
            logger.info(
                f"OPERATION_INFO: READ_ONLY_PERMISSION_GRANTED | message=Read-only permission granted for method: {request.method} | metadata={{'method': '{request.method}', 'is_authenticated': {request.user.is_authenticated if request.user else False}, 'permission_class': '{self.__class__.__name__}'}}"
            )
            return True

        # Write permissions require authenticated user with valid status
        user_status_permission = UserStatusPermission()
        return user_status_permission.has_permission(request, view)


class RoleBasedPermission(UserStatusPermission):
    """
    Base class for role-based permissions with user status validation.
    """

    allowed_roles = []  # Override in subclasses

    def has_permission(self, request, view):
        # First check user status
        if not super().has_permission(request, view):
            return False

        # Check if user has required role
        if request.user.role not in self.allowed_roles:
            logger.warning(
                f"SECURITY_EVENT: ROLE_PERMISSION_DENIED | description=Role permission denied for {request.user.email}: role {request.user.role} not in {self.allowed_roles} | user={request.user.email} | metadata={{'user_role': '{request.user.role}', 'allowed_roles': {self.allowed_roles}, 'permission_class': '{self.__class__.__name__}'}}"
            )
            return False

        # Log successful role-based permission
        logger.info(
            f"OPERATION_INFO: ROLE_BASED_PERMISSION_GRANTED | message=Role-based permission granted for user: {request.user.email} | metadata={{'user_id': {request.user.id}, 'user_email': '{request.user.email}', 'user_role': '{request.user.role}', 'allowed_roles': {self.allowed_roles}, 'permission_class': '{self.__class__.__name__}'}}"
        )

        return True


class FarmerOnlyPermission(RoleBasedPermission):
    """Permission that only allows farmers"""

    allowed_roles = ["farmer", "admin"]


class TraderOnlyPermission(RoleBasedPermission):
    """Permission that only allows traders"""

    allowed_roles = ["trader", "admin"]


class ManufacturerOnlyPermission(RoleBasedPermission):
    """Permission that only allows manufacturers"""

    allowed_roles = ["manufacturer", "admin"]


class FarmerOrTraderPermission(RoleBasedPermission):
    """Permission that allows farmers and traders"""

    allowed_roles = ["farmer", "trader", "admin"]


class TraderOrManufacturerPermission(RoleBasedPermission):
    """Permission that allows traders and manufacturers"""

    allowed_roles = ["trader", "manufacturer", "admin"]


class AllRolesPermission(RoleBasedPermission):
    """Permission that allows all user roles"""

    allowed_roles = ["farmer", "trader", "manufacturer", "admin"]
