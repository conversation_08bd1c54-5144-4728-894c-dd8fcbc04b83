#!/usr/bin/env python
"""
Test Runner for User Registration Services

This script runs all tests for the refactored registration services.
"""

import os
import sys
import django
from django.conf import settings
from django.test.utils import get_runner


def run_tests():
    """Run all registration service tests"""
    
    # Setup Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agritram.settings')
    django.setup()
    
    # Get the Django test runner
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    
    # Define test modules to run
    test_modules = [
        'user.tests.test_registration_validation_service',
        'user.tests.test_device_management_service', 
        'user.tests.test_user_creation_service',
        'user.tests.test_registration_integration',
    ]
    
    print("Running Registration Service Tests...")
    print("=" * 50)
    
    # Run tests
    failures = test_runner.run_tests(test_modules)
    
    if failures:
        print(f"\n❌ {failures} test(s) failed!")
        sys.exit(1)
    else:
        print("\n✅ All tests passed!")
        sys.exit(0)


if __name__ == '__main__':
    run_tests()
