import { create } from 'zustand'
import { persist } from 'zustand/middleware'

// User interface based on the login response
export interface User {
  id: number
  name: string
  email: string
  role: string
  account_address?: string
  opt_in?: boolean
  onboarding_completed?: boolean
}

// Login response interface
export interface LoginResponse {
  token: string // For backward compatibility
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  refresh_expires_in: number
  message: string
  user: User
}

// Token data interface for secure storage
export interface TokenData {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  refresh_expires_in: number
  expires_at: number // Calculated expiration timestamp
  refresh_expires_at: number // Calculated refresh expiration timestamp
}

// Auth store state interface
interface AuthState {
  // Authentication status
  isAuthenticated: boolean
  loading: boolean
  error: string | null

  // User data
  user: User | null
  token: string | null // For backward compatibility
  tokenData: TokenData | null // New JWT token data

  // Actions
  login: (loginData: LoginResponse) => void
  logout: () => void
  setUser: (user: User) => void
  setToken: (token: string) => void
  setTokenData: (tokenData: TokenData) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
  checkAuth: () => Promise<void>
  updateUser: (userData: Partial<User>) => void
  refreshToken: () => Promise<boolean>
  isTokenExpired: () => boolean
  isRefreshTokenExpired: () => boolean
  fetchUserData: () => Promise<void>
}

// Create the auth store with persistence
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      isAuthenticated: false,
      loading: false,
      error: null,
      user: null,
      token: null,
      tokenData: null,

      // Login action - handles the complete login response
      login: (loginData: LoginResponse) => {
        const { token, access_token, refresh_token, token_type, expires_in, refresh_expires_in, user } = loginData

        // Calculate expiration timestamps
        const now = Date.now()
        const expires_at = now + (expires_in * 1000)
        const refresh_expires_at = now + (refresh_expires_in * 1000)

        const tokenData: TokenData = {
          access_token,
          refresh_token,
          token_type,
          expires_in,
          refresh_expires_in,
          expires_at,
          refresh_expires_at,
        }

        set({
          isAuthenticated: true,
          user,
          token: access_token, // For backward compatibility
          tokenData,
          loading: false,
          error: null,
        })
      },

      // Logout action - clears all user data
      logout: async () => {
        const { tokenData } = get()

        // Call logout service to invalidate tokens on server
        try {
          const { logout: logoutService } = await import('../services/authService')
          await logoutService(tokenData?.refresh_token)
        } catch (error) {
          console.error('Error during logout service call:', error)
        }

        set({
          isAuthenticated: false,
          user: null,
          token: null,
          tokenData: null,
          loading: false,
          error: null,
        })
        // Remove persisted snapshot
        localStorage.removeItem('trader-auth-storage')
      },

      // Set user data
      setUser: (user: User) => {
        set({ user })
      },

      // Set authentication token (backward compatibility)
      setToken: (token: string) => {
        set({ token })
      },

      // Set token data
      setTokenData: (tokenData: TokenData) => {
        set({
          tokenData,
          token: tokenData.access_token, // Update backward compatibility token
        })
      },

      // Set loading state
      setLoading: (loading: boolean) => {
        set({ loading })
      },

      // Set error message
      setError: (error: string | null) => {
        set({ error })
      },

      // Clear error message
      clearError: () => {
        set({ error: null })
      },

      // Check if access token is expired
      isTokenExpired: () => {
        const { tokenData } = get()
        if (!tokenData) return true
        return Date.now() >= tokenData.expires_at
      },

      // Check if refresh token is expired
      isRefreshTokenExpired: () => {
        const { tokenData } = get()
        if (!tokenData) return true
        return Date.now() >= tokenData.refresh_expires_at
      },

      // Refresh access token using refresh token
      refreshToken: async () => {
        const { tokenData } = get()
        if (!tokenData || get().isRefreshTokenExpired()) {
          get().logout()
          return false
        }

        try {
          const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}/user/refresh-token/`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              refresh_token: tokenData.refresh_token,
            }),
          })

          if (response.ok) {
            const data = await response.json()

            // Calculate new expiration timestamps
            const now = Date.now()
            const expires_at = now + (data.expires_in * 1000)
            const refresh_expires_at = now + (data.refresh_expires_in * 1000)

            const newTokenData: TokenData = {
              access_token: data.access_token,
              refresh_token: data.refresh_token,
              token_type: data.token_type,
              expires_in: data.expires_in,
              refresh_expires_in: data.refresh_expires_in,
              expires_at,
              refresh_expires_at,
            }

            get().setTokenData(newTokenData)
            return true
          } else {
            get().logout()
            return false
          }
        } catch (error) {
          console.error('Token refresh failed:', error)
          get().logout()
          return false
        }
      },

      // Check authentication status on app initialization
      checkAuth: async () => {
        set({ loading: true })

        try {
          const { tokenData, user } = get()

          if (tokenData && user) {
            // Check if tokens are expired
            if (get().isRefreshTokenExpired()) {
              // Refresh token is expired, logout
              get().logout()
              set({ loading: false })
              return
            }

            if (get().isTokenExpired()) {
              // Access token is expired, try to refresh
              const refreshed = await get().refreshToken()
              if (!refreshed) {
                set({ loading: false })
                return
              }
            }

            // Fetch user data from backend
            await get().fetchUserData()

            set({
              isAuthenticated: true,
              loading: false,
            })
          } else {
            set({
              isAuthenticated: false,
              loading: false,
            })
          }
        } catch (error) {
          console.error('Auth check failed:', error)
          set({
            isAuthenticated: false,
            user: null,
            token: null,
            tokenData: null,
            loading: false,
            error: 'Authentication check failed',
          })
        }
      },

      // Fetch user data from backend using valid token
      fetchUserData: async () => {
        const { tokenData } = get()
        if (!tokenData) return

        try {
          const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}/user/profile/`, {
            headers: {
              'Authorization': `Bearer ${tokenData.access_token}`,
              'Content-Type': 'application/json',
            },
          })

          if (response.ok) {
            const responseData = await response.json()
            // Handle the standardized response format
            const userData = responseData.data || responseData.user || responseData
            set({ user: userData })
          } else if (response.status === 401) {
            // Token might be expired, try to refresh
            const refreshed = await get().refreshToken()
            if (refreshed) {
              // Retry fetching user data
              await get().fetchUserData()
            }
          }
        } catch (error) {
          console.error('Error fetching user data:', error)
        }
      },

      // Update user data partially
      updateUser: (userData: Partial<User>) => {
        const currentUser = get().user
        if (currentUser) {
          const updatedUser = { ...currentUser, ...userData }
          set({ user: updatedUser })
        }
      },
    }),
    {
      name: 'trader-auth-storage', // localStorage key
      partialize: (state) => ({
        // Only store token data and authentication status - NO user information
        token: state.token,
        tokenData: state.tokenData,
        isAuthenticated: state.isAuthenticated,
      }),
    },
  ),
)

// Selector hooks for specific parts of the state
export const useUser = () => useAuthStore((state) => state.user)
export const useToken = () => useAuthStore((state) => state.token)
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated)
export const useAuthLoading = () => useAuthStore((state) => state.loading)
export const useAuthError = () => useAuthStore((state) => state.error)

// Helper hooks for onboarding flow
export const useIsOnboardingCompleted = () => {
  const user = useAuthStore((state) => state.user)
  return user ? !!user.onboarding_completed : false
}

export const useRequiresOnboarding = () => {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)
  const user = useAuthStore((state) => state.user)
  return isAuthenticated && user && !user.onboarding_completed
}
