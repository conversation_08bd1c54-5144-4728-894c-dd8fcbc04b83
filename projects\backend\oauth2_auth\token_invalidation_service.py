"""
Token Invalidation Service

Provides immediate token invalidation capabilities for security incidents,
compromised tokens, and emergency security responses.
"""

from typing import List, Dict, Any, Optional
from datetime import timedelta
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db import transaction
from django.core.cache import cache
from oauth2_provider.models import AccessToken, RefreshToken
from .models import SecureToken, DeviceToken, SecurityEvent
from .utils import log_security_event, get_client_ip
from .config import oauth2_security_config
import logging
import json

logger = logging.getLogger(__name__)
User = get_user_model()


class TokenInvalidationService:
    """
    Service for immediate token invalidation and security incident response
    """

    # Use centralized configuration
    BLACKLIST_CACHE_PREFIX = oauth2_security_config.BLACKLIST_CACHE_PREFIX
    BLACKLIST_CACHE_TIMEOUT = oauth2_security_config.BLACKLIST_CACHE_TIMEOUT

    # Invalidation reasons - use centralized config
    SECURITY_BREACH = oauth2_security_config.INVALIDATION_REASONS["security_breach"]
    COMPROMISED_DEVICE = oauth2_security_config.INVALIDATION_REASONS[
        "compromised_device"
    ]
    SUSPICIOUS_ACTIVITY = oauth2_security_config.INVALIDATION_REASONS[
        "suspicious_activity"
    ]
    ADMIN_ACTION = oauth2_security_config.INVALIDATION_REASONS["admin_action"]
    USER_REQUEST = oauth2_security_config.INVALIDATION_REASONS["user_request"]
    ACCOUNT_LOCKOUT = oauth2_security_config.INVALIDATION_REASONS["account_lockout"]
    PASSWORD_CHANGE = oauth2_security_config.INVALIDATION_REASONS["password_change"]

    @classmethod
    def invalidate_user_tokens(
        cls, user, reason: str, admin_user=None, request=None
    ) -> Dict[str, Any]:
        """
        Invalidate all tokens for a specific user

        Args:
            user: User instance
            reason: Reason for invalidation
            admin_user: Admin user performing the action (optional)
            request: HTTP request object (optional)

        Returns:
            dict: Invalidation results
        """
        
        logger.info(
            f"TOKEN_INVALIDATION_START: Starting token invalidation for user {user.email} | metadata={json.dumps({...})}"
        )

        try:
            with transaction.atomic():
                results = {
                    "oauth2_access_tokens": 0,
                    "oauth2_refresh_tokens": 0,
                    "secure_tokens": 0,
                    "device_tokens": 0,
                    "total_invalidated": 0,
                }

                # Invalidate OAuth2 access tokens
                access_tokens = AccessToken.objects.filter(user=user)
                for token in access_tokens:
                    cls._blacklist_token(token.token, reason)
                access_token_count = access_tokens.count()
                access_tokens.delete()
                results["oauth2_access_tokens"] = access_token_count

                # Log database operation
                logger.info(
                    f"DATABASE_OPERATION: DELETE on oauth2_provider_accesstoken | result=SUCCESS | metadata={json.dumps({...})}"
                )

                # Invalidate OAuth2 refresh tokens
                refresh_tokens = RefreshToken.objects.filter(user=user)
                for token in refresh_tokens:
                    cls._blacklist_token(token.token, reason)
                refresh_token_count = refresh_tokens.count()
                refresh_tokens.delete()
                results["oauth2_refresh_tokens"] = refresh_token_count

                # Log database operation
                logger.info(
                    f"DATABASE_OPERATION: DELETE on oauth2_provider_refreshtoken | result=SUCCESS | metadata={json.dumps({...})}"
                )

                # Invalidate secure tokens
                secure_tokens = SecureToken.objects.filter(user=user, status="active")
                secure_token_count = secure_tokens.count()
                for token in secure_tokens:
                    token.invalidate(reason)
                results["secure_tokens"] = secure_token_count

                # Log database operation
                logger.info(
                    f"DATABASE_OPERATION: UPDATE on oauth2_auth_securetoken | result=SUCCESS | metadata={json.dumps({...})}"
                )

                # Invalidate device tokens
                device_tokens = DeviceToken.objects.filter(user=user, is_active=True)
                device_token_count = device_tokens.count()
                device_tokens.update(is_active=False, deactivated_at=timezone.now())
                results["device_tokens"] = device_token_count

                # Log database operation
                logger.info(
                    f"DATABASE_OPERATION: UPDATE on oauth2_auth_devicetoken | result=SUCCESS | metadata={json.dumps({...})}"
                )

                results["total_invalidated"] = (
                    sum(results.values()) - results["total_invalidated"]
                )

                # Log comprehensive security event
                logger.warning(
                    f"SECURITY_EVENT: ALL_TOKENS_INVALIDATED | description=All user tokens invalidated: {reason} | user={user} | metadata={json.dumps({...})}"
                )

                # Log business event
                log_security_event(
                    user=user,
                    event_type="TOKEN_INVALIDATION_COMPLETED",
                    description=f"Token invalidation completed for user {user.email}",
                    ip_address=get_client_ip(request) if request else None,
                    user_agent=(
                        request.META.get("HTTP_USER_AGENT", "") if request else ""
                    ),
                    device_id=None,
                    metadata={
                        "reason": reason,
                        "admin_user": admin_user.email if admin_user else None,
                        "invalidation_results": results,
                        "total_tokens_invalidated": results["total_invalidated"],
                    },
                    level="WARNING",
                )

                logger.info(
                    f"TOKEN_INVALIDATION_SUCCESS: Successfully invalidated all tokens for user {user.email} | metadata={json.dumps({...})}"
                )

                return results

        except Exception as e:
            logger.error(
                f"TOKEN_INVALIDATION_ERROR: Error invalidating user tokens: {str(e)} | metadata={json.dumps({...})}"
            )
            return {"error": str(e)}

    @classmethod
    def invalidate_specific_token(
        cls,
        token_value: str,
        token_type: str,
        reason: str,
        admin_user=None,
        request=None,
    ) -> bool:
        """
        Invalidate a specific token

        Args:
            token_value: Token value to invalidate
            token_type: Type of token ('access', 'refresh', 'secure', 'device')
            reason: Reason for invalidation
            admin_user: Admin user performing the action (optional)
            request: HTTP request object (optional)

        Returns:
            bool: Success status
        """
        logger.info(
            f"SPECIFIC_TOKEN_INVALIDATION_START: Starting specific token invalidation: {token_type} | metadata={json.dumps({...})}"
        )

        try:
            success = False
            user = None

            if token_type == "access":
                try:
                    token = AccessToken.objects.get(token=token_value)
                    user = token.user
                    cls._blacklist_token(token_value, reason)
                    token.delete()
                    success = True

                    logger.info(
                        f"DATABASE_OPERATION: DELETE on oauth2_provider_accesstoken | result=SUCCESS | metadata={json.dumps({...})}"
                    )
                except AccessToken.DoesNotExist:
                    logger.warning(
                        f"TOKEN_NOT_FOUND: Access token not found for invalidation | metadata={json.dumps({...})}"
                    )

            elif token_type == "refresh":
                try:
                    token = RefreshToken.objects.get(token=token_value)
                    user = token.user
                    cls._blacklist_token(token_value, reason)
                    token.delete()
                    success = True

                    logger.info(
                        f"DATABASE_OPERATION: DELETE on oauth2_provider_refreshtoken | result=SUCCESS | metadata={json.dumps({...})}"
                    )   
                except RefreshToken.DoesNotExist:
                    logger.warning(
                        f"TOKEN_NOT_FOUND: Refresh token not found for invalidation | metadata={json.dumps({...})}"
                    )

            elif token_type == "secure":
                from .utils import hash_token

                token_hash = hash_token(token_value)
                try:
                    token = SecureToken.objects.get(
                        token_hash=token_hash, status="active"
                    )
                    user = token.user
                    token.invalidate(reason)
                    success = True

                    logger.info(
                        f"DATABASE_OPERATION: UPDATE on oauth2_auth_securetoken | result=SUCCESS | metadata={json.dumps({...})}"
                    )
                except SecureToken.DoesNotExist:
                    logger.warning(
                        f"TOKEN_NOT_FOUND: Secure token not found for invalidation | metadata={json.dumps({...})}"
                    )

            if success and user:
                log_security_event(
                    user=user,
                    event_type="SPECIFIC_TOKEN_INVALIDATED",
                    description=f"Specific {token_type} token invalidated: {reason}",
                    ip_address=get_client_ip(request) if request else None,
                    user_agent=(
                        request.META.get("HTTP_USER_AGENT", "") if request else ""
                    ),
                    device_id=None,
                    metadata={
                        "token_type": token_type,
                        "reason": reason,
                        "admin_user": admin_user.email if admin_user else None,
                        "operation": "invalidate_specific_token",
                    },
                    level="WARNING",
                )

                log_security_event(
                    user=user,
                    event_type="SPECIFIC_TOKEN_INVALIDATED",
                    description=f"Specific {token_type} token invalidated for user {user.email}",
                    ip_address=get_client_ip(request) if request else None,
                    user_agent=(
                        request.META.get("HTTP_USER_AGENT", "") if request else ""
                    ),
                    device_id=None,
                    metadata={
                        "token_type": token_type,
                        "reason": reason,
                        "user_id": user.id,
                        "admin_user": admin_user.email if admin_user else None,
                    },
                    level="WARNING",
                )

                logger.info(
                    f"SPECIFIC_TOKEN_INVALIDATION_SUCCESS: Successfully invalidated {token_type} token for user {user.email} | metadata={json.dumps({...})}"
                )
            else:
                logger.warning(
                    f"SPECIFIC_TOKEN_INVALIDATION_FAILED: Failed to invalidate {token_type} token | metadata={json.dumps({...})}"
                )

            return success

        except Exception as e:
            logger.error(
                f"SPECIFIC_TOKEN_INVALIDATION_ERROR: Error invalidating specific token: {str(e)} | metadata={json.dumps({...})}"
            )
            return False

    @classmethod
    def invalidate_device_tokens(
        cls, device_id: str, reason: str, admin_user=None, request=None
    ) -> int:
        """
        Invalidate all tokens for a specific device

        Args:
            device_id: Device identifier
            reason: Reason for invalidation
            admin_user: Admin user performing the action (optional)
            request: HTTP request object (optional)

        Returns:
            int: Number of tokens invalidated
        """
        logger.info(
            f"DEVICE_TOKEN_INVALIDATION_START: Starting device token invalidation for device {device_id} | metadata={json.dumps({...})}"
        )

        try:
            with transaction.atomic():
                # Invalidate device tokens
                device_tokens = DeviceToken.objects.filter(
                    device_id=device_id, is_active=True
                )
                count = device_tokens.count()

                users_affected = []
                for token in device_tokens:
                    users_affected.append(token.user)

                device_tokens.update(is_active=False, deactivated_at=timezone.now())

                # Log database operation
                logger.info(
                    f"DATABASE_OPERATION: UPDATE on oauth2_auth_devicetoken | result=SUCCESS | metadata={json.dumps({...})}"
                )

                # Invalidate OAuth2 tokens for this device (if we can identify them)
                # This is more complex as OAuth2 tokens don't directly link to devices
                # We'll log the event for manual review if needed

                # Log security events for affected users
                for user in set(users_affected):
                    log_security_event(
                        user=user,
                        event_type="device_tokens_invalidated",
                        description=f"Device tokens invalidated: {reason}",
                        ip_address=get_client_ip(request) if request else None,
                        user_agent=(
                            request.META.get("HTTP_USER_AGENT", "") if request else ""
                        ),
                        device_id=device_id,
                        metadata={
                            "reason": reason,
                            "admin_user": admin_user.email if admin_user else None,
                            "tokens_invalidated": count,
                        },
                    )

                    # Also log using standardized security event logging
                    logger.warning(
                        f"SECURITY_EVENT: DEVICE_TOKENS_INVALIDATED | description=Device tokens invalidated for user {user.email}: {reason} | user={user} | metadata={json.dumps({...})}"
                    )

                logger.info(
                    f"DEVICE_TOKEN_INVALIDATION_SUCCESS: Successfully invalidated {count} device tokens for device {device_id} | metadata={json.dumps({...})}"
                )

                return count

        except Exception as e:
            logger.error(
                f"DEVICE_TOKEN_INVALIDATION_ERROR: Error invalidating device tokens: {str(e)} | metadata={json.dumps({...})}"
            )
            return 0

    @classmethod
    def is_token_blacklisted(cls, token_value: str) -> bool:
        """
        Check if a token is blacklisted

        Args:
            token_value: Token value to check

        Returns:
            bool: True if token is blacklisted
        """
        try:
            cache_key = f"{cls.BLACKLIST_CACHE_PREFIX}{hash(token_value)}"
            blacklist_data = cache.get(cache_key)
            is_blacklisted = blacklist_data is not None

            logger.info(
                f"TOKEN_BLACKLIST_CHECK: Token blacklist check completed | metadata={json.dumps({...})}"
            )

            return is_blacklisted

        except Exception as e:
            logger.error(
                f"TOKEN_BLACKLIST_CHECK_ERROR: Error checking token blacklist: {str(e)} | metadata={json.dumps({...})}"
            )
            return False

    @classmethod
    def emergency_invalidate_all(
        cls, reason: str, admin_user, request=None
    ) -> Dict[str, Any]:
        """
        Emergency function to invalidate ALL tokens in the system

        Args:
            reason: Reason for emergency invalidation
            admin_user: Admin user performing the action
            request: HTTP request object (optional)

        Returns:
            dict: Invalidation results
        """
        try:
            with transaction.atomic():
                results = {
                    "oauth2_access_tokens": 0,
                    "oauth2_refresh_tokens": 0,
                    "secure_tokens": 0,
                    "device_tokens": 0,
                    "total_invalidated": 0,
                }

                # Invalidate all OAuth2 access tokens
                access_tokens = AccessToken.objects.all()
                for token in access_tokens:
                    cls._blacklist_token(token.token, reason)
                results["oauth2_access_tokens"] = access_tokens.count()
                access_tokens.delete()

                # Invalidate all OAuth2 refresh tokens
                refresh_tokens = RefreshToken.objects.all()
                for token in refresh_tokens:
                    cls._blacklist_token(token.token, reason)
                results["oauth2_refresh_tokens"] = refresh_tokens.count()
                refresh_tokens.delete()

                # Invalidate all secure tokens
                secure_tokens = SecureToken.objects.filter(status="active")
                for token in secure_tokens:
                    token.invalidate(reason)
                results["secure_tokens"] = secure_tokens.count()

                # Invalidate all device tokens
                device_tokens = DeviceToken.objects.filter(is_active=True)
                results["device_tokens"] = device_tokens.count()
                device_tokens.update(is_active=False, deactivated_at=timezone.now())

                results["total_invalidated"] = (
                    sum(results.values()) - results["total_invalidated"]
                )

                # Log critical security event with comprehensive logging
                logger.error(
                    f"SECURITY_EVENT: EMERGENCY_ALL_TOKENS_INVALIDATED | description=EMERGENCY: All system tokens invalidated: {reason} | request={request} | metadata={json.dumps({...})}"
                )

                log_security_event(
                    user=admin_user,
                    event_type="EMERGENCY_TOKEN_INVALIDATION",
                    description=f"EMERGENCY: All system tokens invalidated by {admin_user.email}",
                    ip_address=get_client_ip(request) if request else None,
                    user_agent=(
                        request.META.get("HTTP_USER_AGENT", "") if request else ""
                    ),
                    device_id=None,
                    metadata={
                        "reason": reason,
                        "admin_user": admin_user.email,
                        "invalidation_results": results,
                        "total_tokens_invalidated": results["total_invalidated"],
                        "severity": "CRITICAL",
                    },
                    level="ERROR",
                )

                logger.info(
                    f"EMERGENCY_TOKEN_INVALIDATION_SUCCESS: EMERGENCY: All tokens invalidated by {admin_user.email} | metadata={json.dumps({...})}"
                )

                return results

        except Exception as e:
            logger.error(
                f"EMERGENCY_TOKEN_INVALIDATION_ERROR: Error in emergency token invalidation: {str(e)} | metadata={json.dumps({...})}"
            )
            return {"error": str(e)}

    @classmethod
    def _blacklist_token(cls, token_value: str, reason: str):
        """Add token to blacklist cache"""
        try:
            cache_key = f"{cls.BLACKLIST_CACHE_PREFIX}{hash(token_value)}"
            blacklist_data = {
                "reason": reason,
                "blacklisted_at": timezone.now().isoformat(),
            }

            cache.set(
                cache_key,
                blacklist_data,
                cls.BLACKLIST_CACHE_TIMEOUT,
            )

            logger.info(
                f"TOKEN_BLACKLIST_SUCCESS: Token successfully blacklisted | metadata={json.dumps({...})}"
            )

        except Exception as e:
            logger.error(
                f"TOKEN_BLACKLIST_ERROR: Error blacklisting token: {str(e)} | metadata={json.dumps({...})}"
            )


# Global service instance
token_invalidation_service = TokenInvalidationService()
