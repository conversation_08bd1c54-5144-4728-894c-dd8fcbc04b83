"""
Correlation ID utilities for easy access throughout the application.
"""

from .correlation_id_middleware import get_correlation_id, set_correlation_id
import logging
logger = logging.getLogger(__name__)

def get_request_correlation_id():
    """
    Get the current request's correlation ID.
    
    Returns:
        str: Current correlation ID or None if not set
    """
    return get_correlation_id()


def log_with_correlation_id(message, level="INFO", operation_type="GENERAL", metadata=None):
    """
    Log a message with automatic correlation ID inclusion.
    
    Args:
        message (str): Log message
        level (str): Log level (INFO, WARNING, ERROR)
        operation_type (str): Type of operation being logged
        metadata (dict): Additional metadata
    """
    correlation_id = get_correlation_id()
    if metadata is None:
        metadata = {}
    
    metadata['correlation_id'] = correlation_id
    
    logger.info(
        message=message,
        level=level,
        operation_type=operation_type,
        metadata=metadata
    )


def add_correlation_id_to_response_headers(response):
    """
    Manually add correlation ID to response headers.
    This is automatically done by the middleware, but can be used
    for custom responses.
    
    Args:
        response: Django HttpResponse object
        
    Returns:
        response: Modified response with correlation ID header
    """
    correlation_id = get_correlation_id()
    if correlation_id:
        response['X-Correlation-ID'] = correlation_id
    return response


def create_correlation_context(correlation_id=None):
    """
    Create a context manager for setting correlation ID.
    
    Args:
        correlation_id (str): Optional correlation ID to set
        
    Returns:
        CorrelationContext: Context manager
    """
    return CorrelationContext(correlation_id)


class CorrelationContext:
    """
    Context manager for setting correlation ID in a specific scope.
    
    Usage:
        with create_correlation_context("custom-id"):
            # All logging and operations in this block will use "custom-id"
            log_with_correlation_id("This will include the custom correlation ID")
    """
    
    def __init__(self, correlation_id=None):
        self.correlation_id = correlation_id
        self.previous_id = None
    
    def __enter__(self):
        self.previous_id = get_correlation_id()
        if self.correlation_id:
            set_correlation_id(self.correlation_id)
        return self.correlation_id or self.previous_id
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.previous_id:
            set_correlation_id(self.previous_id)


# Convenience functions for common logging patterns
def log_info_with_cid(message, metadata=None):
    """Log info message with correlation ID."""
    log_with_correlation_id(message, "INFO", "INFO", metadata)


def log_warning_with_cid(message, metadata=None):
    """Log warning message with correlation ID."""
    log_with_correlation_id(message, "WARNING", "WARNING", metadata)


def log_error_with_cid(message, metadata=None):
    """Log error message with correlation ID."""
    log_with_correlation_id(message, "ERROR", "ERROR", metadata)


def log_business_event_with_cid(event_type, description, entity_type=None, entity_id=None, metadata=None):
    """
    Log business event with correlation ID.
    
    Args:
        event_type (str): Type of business event
        description (str): Event description
        entity_type (str): Type of entity involved
        entity_id (str): ID of entity involved
        metadata (dict): Additional metadata
    """
    if metadata is None:
        metadata = {}
    
    metadata.update({
        'event_type': event_type,
        'entity_type': entity_type,
        'entity_id': entity_id,
        'correlation_id': get_correlation_id()
    })
    
    log_with_correlation_id(
        message=f"Business Event: {event_type} - {description}",
        level="INFO",
        operation_type="BUSINESS_EVENT",
        metadata=metadata
    )
